package com.signaturedetection;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeAll;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Example test class demonstrating how to test signature detection results
 * against expected coordinates and validate detection accuracy.
 */
public class CoordinateTestExample {
    
    @BeforeAll
    static void setUp() {
        // Load OpenCV
        nu.pattern.OpenCV.loadShared();
    }
    
    /**
     * Example test showing how to validate signature detection results
     * against expected coordinates.
     */
    @Test
    void testSignatureDetectionAgainstExpectedCoordinates() {
        // This is an example of how you would test against known coordinates
        // Replace with your actual test image path and expected coordinates
        
        String testImagePath = "test_document.tiff"; // Replace with actual test image
        String outputDir = "./test_output/";
        
        // Expected signature coordinates (replace with your actual expected values)
        ExpectedSignature[] expectedSignatures = {
            new ExpectedSignature(1, 150, 200, 120, 40),  // x, y, width, height
            new ExpectedSignature(2, 300, 450, 100, 35),
            new ExpectedSignature(3, 500, 600, 140, 50)
        };
        
        try {
            // Process the document and get results
            SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithResult(testImagePath, outputDir);
            
            // Validate the result
            assertTrue(result.isSuccess(), "Processing should succeed");
            
            // Test signature count
            assertEquals(expectedSignatures.length, result.getSignatureCount(), 
                        "Should detect expected number of signatures");
            
            // Test each detected signature against expected coordinates
            for (ExpectedSignature expected : expectedSignatures) {
                SignatureDetectionResult.DetectedSignature detected = 
                    findClosestSignature(result, expected.x, expected.y);
                
                assertNotNull(detected, "Should find signature near expected position");
                
                // Test coordinate accuracy (allow some tolerance)
                int tolerance = 20; // pixels
                assertTrue(Math.abs(detected.getX() - expected.x) <= tolerance,
                          String.format("X coordinate should be within tolerance. Expected: %d, Actual: %d", 
                                      expected.x, detected.getX()));
                
                assertTrue(Math.abs(detected.getY() - expected.y) <= tolerance,
                          String.format("Y coordinate should be within tolerance. Expected: %d, Actual: %d", 
                                      expected.y, detected.getY()));
                
                // Test size accuracy
                int sizeTolerance = 30; // pixels
                assertTrue(Math.abs(detected.getWidth() - expected.width) <= sizeTolerance,
                          "Width should be within tolerance");
                
                assertTrue(Math.abs(detected.getHeight() - expected.height) <= sizeTolerance,
                          "Height should be within tolerance");
            }
            
            // Print results for manual verification
            System.out.println("Detection Results:");
            System.out.println(result.toJsonString());
            
        } catch (Exception e) {
            fail("Processing should not throw exception: " + e.getMessage());
        }
    }
    
    /**
     * Test signature detection in specific regions of the document.
     */
    @Test
    void testSignatureDetectionInSpecificRegions() {
        String testImagePath = "test_document.tiff";
        String outputDir = "./test_output/";
        
        try {
            SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithResult(testImagePath, outputDir);
            
            if (result.isSuccess()) {
                // Test signatures in top half of document
                int imageHeight = result.getStats().getImageHeight();
                var topHalfSignatures = result.getSignaturesInArea(0, 0, 
                                                                  result.getStats().getImageWidth(), 
                                                                  imageHeight / 2);
                
                // Test signatures in bottom half
                var bottomHalfSignatures = result.getSignaturesInArea(0, imageHeight / 2,
                                                                     result.getStats().getImageWidth(),
                                                                     imageHeight / 2);
                
                System.out.println("Signatures in top half: " + topHalfSignatures.size());
                System.out.println("Signatures in bottom half: " + bottomHalfSignatures.size());
                
                // Add your specific assertions based on expected distribution
                // assertTrue(topHalfSignatures.size() >= 1, "Should have signatures in top half");
            }
            
        } catch (Exception e) {
            fail("Processing should not throw exception: " + e.getMessage());
        }
    }
    
    /**
     * Test signature properties like size and aspect ratio.
     */
    @Test
    void testSignatureProperties() {
        String testImagePath = "test_document.tiff";
        String outputDir = "./test_output/";
        
        try {
            SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithResult(testImagePath, outputDir);
            
            if (result.isSuccess() && result.getSignatureCount() > 0) {
                // Test that all signatures meet minimum size requirements
                for (SignatureDetectionResult.DetectedSignature signature : result.getSignatures()) {
                    assertTrue(signature.getArea() >= 300, 
                              "Signature should meet minimum area requirement");
                    
                    assertTrue(signature.getAspectRatio() >= 1.0, 
                              "Signature should have reasonable aspect ratio");
                    
                    assertTrue(signature.getWidth() >= 20 && signature.getHeight() >= 10,
                              "Signature should meet minimum dimension requirements");
                }
                
                // Test for overlapping signatures (usually indicates false positives)
                var signatures = result.getSignatures();
                for (int i = 0; i < signatures.size(); i++) {
                    for (int j = i + 1; j < signatures.size(); j++) {
                        assertFalse(signatures.get(i).overlaps(signatures.get(j)),
                                   "Signatures should not overlap significantly");
                    }
                }
            }
            
        } catch (Exception e) {
            fail("Processing should not throw exception: " + e.getMessage());
        }
    }
    
    /**
     * Example of how to test processing performance.
     */
    @Test
    void testProcessingPerformance() {
        String testImagePath = "test_document.tiff";
        String outputDir = "./test_output/";
        
        try {
            SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithResult(testImagePath, outputDir);
            
            if (result.isSuccess()) {
                // Test processing time is reasonable
                long processingTime = result.getStats().getProcessingTimeMs();
                assertTrue(processingTime < 30000, // 30 seconds max
                          "Processing should complete within reasonable time");
                
                System.out.println("Processing completed in: " + processingTime + "ms");
                
                // Test that image was processed correctly
                assertTrue(result.getStats().getImageWidth() > 0, "Image should have valid dimensions");
                assertTrue(result.getStats().getImageHeight() > 0, "Image should have valid dimensions");
                assertTrue(result.getStats().isLinesRemoved(), "Lines should be removed");
            }
            
        } catch (Exception e) {
            fail("Processing should not throw exception: " + e.getMessage());
        }
    }
    
    // Helper classes and methods
    
    /**
     * Helper class to define expected signature coordinates for testing.
     */
    private static class ExpectedSignature {
        final int id;
        final int x, y, width, height;
        
        ExpectedSignature(int id, int x, int y, int width, int height) {
            this.id = id;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
    }
    
    /**
     * Helper method to find the closest detected signature to expected coordinates.
     */
    private SignatureDetectionResult.DetectedSignature findClosestSignature(
            SignatureDetectionResult result, int expectedX, int expectedY) {
        
        SignatureDetectionResult.DetectedSignature closest = null;
        double minDistance = Double.MAX_VALUE;
        
        for (SignatureDetectionResult.DetectedSignature signature : result.getSignatures()) {
            double distance = Math.sqrt(Math.pow(signature.getX() - expectedX, 2) + 
                                      Math.pow(signature.getY() - expectedY, 2));
            if (distance < minDistance) {
                minDistance = distance;
                closest = signature;
            }
        }
        
        return closest;
    }
    
    /**
     * Example of how to create a comprehensive test report.
     */
    public static void generateTestReport(SignatureDetectionResult result, String outputPath) {
        try (java.io.PrintWriter writer = new java.io.PrintWriter(outputPath)) {
            writer.println("Signature Detection Test Report");
            writer.println("==============================");
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println();
            
            if (result.isSuccess()) {
                writer.println("Processing Status: SUCCESS");
                writer.println("Processing Time: " + result.getStats().getProcessingTimeMs() + "ms");
                writer.println("Image Size: " + result.getStats().getImageWidth() + "x" + result.getStats().getImageHeight());
                writer.println("Total Contours Found: " + result.getStats().getTotalContours());
                writer.println("Signatures Detected: " + result.getSignatureCount());
                writer.println();
                
                writer.println("Detected Signatures:");
                writer.println("-------------------");
                for (SignatureDetectionResult.DetectedSignature sig : result.getSignatures()) {
                    writer.println(sig.toString());
                }
            } else {
                writer.println("Processing Status: FAILED");
                writer.println("Error: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            System.err.println("Failed to generate test report: " + e.getMessage());
        }
    }
}
