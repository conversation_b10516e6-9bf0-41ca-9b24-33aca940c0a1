package com.signaturedetection.examples;

import com.signaturedetection.*;
import org.opencv.core.Mat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Example demonstrating different noise removal techniques and their effects
 * on signature detection accuracy.
 */
public class NoiseRemovalExample {
    private static final Logger logger = LoggerFactory.getLogger(NoiseRemovalExample.class);
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java NoiseRemovalExample <input-tiff-path>");
            System.out.println("Example: java NoiseRemovalExample noisy_document.tiff");
            return;
        }
        
        String inputPath = args[0];
        String outputDir = "./noise_removal_output/";
        
        try {
            demonstrateNoiseRemovalTechniques(inputPath, outputDir);
        } catch (Exception e) {
            logger.error("Error in noise removal example: {}", e.getMessage(), e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Demonstrates various noise removal techniques and compares their effectiveness.
     */
    public static void demonstrateNoiseRemovalTechniques(String inputPath, String outputDir) throws IOException {
        System.out.println("=== Noise Removal Techniques Demonstration ===");
        System.out.println("Input: " + inputPath);
        System.out.println("Output: " + outputDir);
        System.out.println();
        
        // Create output directory
        java.io.File outputDirectory = new java.io.File(outputDir);
        if (!outputDirectory.exists()) {
            outputDirectory.mkdirs();
        }
        
        // Load the original image
        Mat originalImage = ImageProcessor.loadTiffImage(inputPath);
        
        // Test 1: No noise removal (baseline)
        System.out.println("=== Test 1: No Noise Removal (Baseline) ===");
        testNoNoiseRemoval(originalImage, outputDir + "/01_no_denoising/");
        
        // Test 2: Light noise removal
        System.out.println("=== Test 2: Light Noise Removal ===");
        testLightNoiseRemoval(originalImage, outputDir + "/02_light_denoising/");
        
        // Test 3: Standard noise removal
        System.out.println("=== Test 3: Standard Noise Removal ===");
        testStandardNoiseRemoval(originalImage, outputDir + "/03_standard_denoising/");
        
        // Test 4: Aggressive noise removal
        System.out.println("=== Test 4: Aggressive Noise Removal ===");
        testAggressiveNoiseRemoval(originalImage, outputDir + "/04_aggressive_denoising/");
        
        // Test 5: Advanced noise removal
        System.out.println("=== Test 5: Advanced Noise Removal ===");
        testAdvancedNoiseRemoval(inputPath, outputDir + "/05_advanced_denoising/");
        
        // Test 6: Custom noise removal configurations
        System.out.println("=== Test 6: Custom Noise Removal Configurations ===");
        testCustomNoiseRemoval(inputPath, outputDir + "/06_custom_denoising/");
        
        // Clean up
        originalImage.release();
        
        System.out.println("=== Noise Removal Demonstration Complete ===");
        System.out.println("Check subdirectories in " + outputDir + " for results");
        
        // Generate comparison report
        generateComparisonReport(outputDir);
    }
    
    /**
     * Test with no noise removal (baseline).
     */
    private static void testNoNoiseRemoval(Mat originalImage, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        // Basic preprocessing without noise removal
        Mat processed = new Mat();
        org.opencv.imgproc.Imgproc.GaussianBlur(originalImage, processed, new org.opencv.core.Size(3, 3), 0);
        
        Mat binary = new Mat();
        org.opencv.imgproc.Imgproc.adaptiveThreshold(processed, binary, 255, 
                                                   org.opencv.imgproc.Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                                   org.opencv.imgproc.Imgproc.THRESH_BINARY, 11, 2);
        org.opencv.core.Core.bitwise_not(binary, binary);
        
        // Save result
        ImageProcessor.saveImage(binary, outputDir + "preprocessed.png");
        
        // Detect signatures
        var signatures = SignatureDetector.detectSignatureRegions(binary);
        System.out.println("  No denoising: " + signatures.size() + " signatures detected");
        
        processed.release();
        binary.release();
    }
    
    /**
     * Test with light noise removal.
     */
    private static void testLightNoiseRemoval(Mat originalImage, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        NoiseRemover.NoiseRemovalConfig lightConfig = NoiseRemover.NoiseRemovalConfig.createLightConfig();
        Mat denoised = NoiseRemover.removeNoise(originalImage, lightConfig);
        
        ImageProcessor.saveImage(denoised, outputDir + "denoised.png");
        
        Mat processed = ImageProcessor.preprocessImage(originalImage, lightConfig);
        ImageProcessor.saveImage(processed, outputDir + "preprocessed.png");
        
        var signatures = SignatureDetector.detectSignatureRegions(processed);
        System.out.println("  Light denoising: " + signatures.size() + " signatures detected");
        
        denoised.release();
        processed.release();
    }
    
    /**
     * Test with standard noise removal.
     */
    private static void testStandardNoiseRemoval(Mat originalImage, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        NoiseRemover.NoiseRemovalConfig standardConfig = new NoiseRemover.NoiseRemovalConfig();
        Mat denoised = NoiseRemover.removeNoise(originalImage, standardConfig);
        
        ImageProcessor.saveImage(denoised, outputDir + "denoised.png");
        
        Mat processed = ImageProcessor.preprocessImage(originalImage, standardConfig);
        ImageProcessor.saveImage(processed, outputDir + "preprocessed.png");
        
        var signatures = SignatureDetector.detectSignatureRegions(processed);
        System.out.println("  Standard denoising: " + signatures.size() + " signatures detected");
        
        denoised.release();
        processed.release();
    }
    
    /**
     * Test with aggressive noise removal.
     */
    private static void testAggressiveNoiseRemoval(Mat originalImage, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        NoiseRemover.NoiseRemovalConfig aggressiveConfig = NoiseRemover.NoiseRemovalConfig.createAggressiveConfig();
        Mat denoised = NoiseRemover.removeNoise(originalImage, aggressiveConfig);
        
        ImageProcessor.saveImage(denoised, outputDir + "denoised.png");
        
        Mat processed = ImageProcessor.preprocessImage(originalImage, aggressiveConfig);
        ImageProcessor.saveImage(processed, outputDir + "preprocessed.png");
        
        var signatures = SignatureDetector.detectSignatureRegions(processed);
        System.out.println("  Aggressive denoising: " + signatures.size() + " signatures detected");
        
        denoised.release();
        processed.release();
    }
    
    /**
     * Test with advanced noise removal techniques.
     */
    private static void testAdvancedNoiseRemoval(String inputPath, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithAdvancedDenoising(inputPath, outputDir);
        
        if (result.isSuccess()) {
            System.out.println("  Advanced denoising: " + result.getSignatureCount() + " signatures detected");
            System.out.println("  Processing time: " + result.getStats().getProcessingTimeMs() + "ms");
        } else {
            System.out.println("  Advanced denoising failed: " + result.getErrorMessage());
        }
    }
    
    /**
     * Test with custom noise removal configurations.
     */
    private static void testCustomNoiseRemoval(String inputPath, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        // Test different custom configurations
        testCustomConfig1(inputPath, outputDir + "/config1/");
        testCustomConfig2(inputPath, outputDir + "/config2/");
        testCustomConfig3(inputPath, outputDir + "/config3/");
    }
    
    private static void testCustomConfig1(String inputPath, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        // Configuration for documents with salt-and-pepper noise
        NoiseRemover.NoiseRemovalConfig config = new NoiseRemover.NoiseRemovalConfig();
        config.medianKernelSize = 5; // Larger median filter
        config.gaussianKernelSize = 3;
        config.morphKernelSize = 2;
        config.minComponentArea = 5;
        config.maxComponentArea = 100;
        
        SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithCustomDenoising(inputPath, outputDir, config);
        
        if (result.isSuccess()) {
            System.out.println("  Custom Config 1 (Salt-Pepper): " + result.getSignatureCount() + " signatures detected");
        }
    }
    
    private static void testCustomConfig2(String inputPath, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        // Configuration for documents with speckle noise
        NoiseRemover.NoiseRemovalConfig config = new NoiseRemover.NoiseRemovalConfig();
        config.gaussianKernelSize = 5; // Larger Gaussian blur
        config.medianKernelSize = 3;
        config.morphKernelSize = 3; // Larger morphological kernel
        config.morphIterations = 2;
        config.minComponentArea = 15;
        config.maxComponentArea = 150;
        
        SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithCustomDenoising(inputPath, outputDir, config);
        
        if (result.isSuccess()) {
            System.out.println("  Custom Config 2 (Speckle): " + result.getSignatureCount() + " signatures detected");
        }
    }
    
    private static void testCustomConfig3(String inputPath, String outputDir) throws IOException {
        new java.io.File(outputDir).mkdirs();
        
        // Configuration for high-quality documents with minimal noise
        NoiseRemover.NoiseRemovalConfig config = new NoiseRemover.NoiseRemovalConfig();
        config.gaussianKernelSize = 3;
        config.gaussianSigma = 0.5; // Minimal blur
        config.medianKernelSize = 3;
        config.morphKernelSize = 1; // Minimal morphological operations
        config.minComponentArea = 3;
        config.maxComponentArea = 50;
        
        SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithCustomDenoising(inputPath, outputDir, config);
        
        if (result.isSuccess()) {
            System.out.println("  Custom Config 3 (Minimal): " + result.getSignatureCount() + " signatures detected");
        }
    }
    
    /**
     * Generate a comparison report of all noise removal techniques.
     */
    private static void generateComparisonReport(String outputDir) throws IOException {
        String reportPath = outputDir + "/noise_removal_comparison_report.txt";
        
        try (java.io.PrintWriter writer = new java.io.PrintWriter(reportPath)) {
            writer.println("Noise Removal Techniques Comparison Report");
            writer.println("=========================================");
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println();
            
            writer.println("This report compares the effectiveness of different noise removal techniques");
            writer.println("for signature detection in scanned documents.");
            writer.println();
            
            writer.println("Techniques Tested:");
            writer.println("1. No Denoising (Baseline) - Basic preprocessing without noise removal");
            writer.println("2. Light Denoising - Minimal noise removal for high-quality scans");
            writer.println("3. Standard Denoising - Balanced approach for typical documents");
            writer.println("4. Aggressive Denoising - Strong noise removal for degraded documents");
            writer.println("5. Advanced Denoising - Comprehensive multi-stage noise removal");
            writer.println("6. Custom Configurations - Specialized settings for specific noise types");
            writer.println();
            
            writer.println("Output Directories:");
            writer.println("- 01_no_denoising/ - Baseline results");
            writer.println("- 02_light_denoising/ - Light noise removal results");
            writer.println("- 03_standard_denoising/ - Standard noise removal results");
            writer.println("- 04_aggressive_denoising/ - Aggressive noise removal results");
            writer.println("- 05_advanced_denoising/ - Advanced noise removal results");
            writer.println("- 06_custom_denoising/ - Custom configuration results");
            writer.println();
            
            writer.println("Recommendations:");
            writer.println("- Use Light Denoising for high-quality scanned documents");
            writer.println("- Use Standard Denoising for typical office documents");
            writer.println("- Use Aggressive Denoising for old or degraded documents");
            writer.println("- Use Advanced Denoising for heavily corrupted documents");
            writer.println("- Use Custom Configurations when you know the specific noise type");
            writer.println();
            
            writer.println("Compare the 'preprocessed.png' files in each directory to see the");
            writer.println("effect of different noise removal techniques on your document.");
        }
        
        System.out.println("Comparison report saved to: " + reportPath);
    }
}
