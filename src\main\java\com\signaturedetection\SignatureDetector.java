package com.signaturedetection;

import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for detecting and extracting signatures from scanned documents.
 * Uses contour analysis and connected component analysis to identify signature regions.
 */
public class SignatureDetector {
    private static final Logger logger = LoggerFactory.getLogger(SignatureDetector.class);
    
    // Default parameters for signature detection
    private static final int MIN_SIGNATURE_AREA = 250;
    private static final int MAX_SIGNATURE_AREA = 50000;
    private static final double MIN_ASPECT_RATIO = 0.5;
    private static final double MAX_ASPECT_RATIO = 8.0;
    private static final int MIN_CONTOUR_POINTS = 15;
    
    /**
     * Detects potential signature regions in the input image.
     * 
     * @param inputImage The preprocessed binary image
     * @return List of rectangles representing potential signature regions
     */
    public static List<Rect> detectSignatureRegions(Mat inputImage) {
        logger.info("Starting signature detection");
        
        List<Rect> signatureRegions = new ArrayList<>();
        
        // Find contours
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();
        Imgproc.findContours(inputImage, contours, hierarchy, 
                           Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);
        
        logger.debug("Found {} contours", contours.size());
        
        // Analyze each contour
        for (MatOfPoint contour : contours) {
            Rect boundingRect = Imgproc.boundingRect(contour);
            double area = Imgproc.contourArea(contour);
            double aspectRatio = (double) boundingRect.width / boundingRect.height;
            
            // Check if contour meets signature criteria
            if (isLikelySignature(boundingRect, area, aspectRatio, contour)) {
                signatureRegions.add(boundingRect);
                logger.debug("Potential signature found: area={}, aspect ratio={}, bounds={}", 
                           area, aspectRatio, boundingRect);
            }
        }
        
        hierarchy.release();
        logger.info("Detected {} potential signature regions", signatureRegions.size());
        return signatureRegions;
    }
    
    /**
     * Extracts signature regions from the original image.
     * 
     * @param originalImage The original image
     * @param signatureRegions List of detected signature regions
     * @return List of extracted signature images
     */
    public static List<Mat> extractSignatures(Mat originalImage, List<Rect> signatureRegions) {
        logger.info("Extracting {} signatures", signatureRegions.size());
        
        List<Mat> signatures = new ArrayList<>();
        
        for (int i = 0; i < signatureRegions.size(); i++) {
            Rect region = signatureRegions.get(i);
            
            // Ensure the region is within image bounds
            Rect clampedRegion = clampRectToImage(region, originalImage);
            
            // Extract the signature region
            Mat signature = new Mat(originalImage, clampedRegion);
            Mat signatureCopy = signature.clone();
            signatures.add(signatureCopy);
            
            logger.debug("Extracted signature {}: {}x{}", i + 1, 
                        clampedRegion.width, clampedRegion.height);
        }
        
        return signatures;
    }
    
    /**
     * Detects signatures with custom parameters for fine-tuning.
     * 
     * @param inputImage The preprocessed binary image
     * @param minArea Minimum area for signature detection
     * @param maxArea Maximum area for signature detection
     * @param minAspectRatio Minimum aspect ratio
     * @param maxAspectRatio Maximum aspect ratio
     * @return List of detected signature regions
     */
    public static List<Rect> detectSignatureRegionsCustom(Mat inputImage, 
                                                         int minArea, int maxArea,
                                                         double minAspectRatio, double maxAspectRatio) {
        logger.info("Detecting signatures with custom parameters: area=[{},{}], aspect=[{},{}]", 
                   minArea, maxArea, minAspectRatio, maxAspectRatio);
        
        List<Rect> signatureRegions = new ArrayList<>();
        
        // Find contours
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();
        Imgproc.findContours(inputImage, contours, hierarchy, 
                           Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);
        
        // Analyze each contour with custom parameters
        for (MatOfPoint contour : contours) {
            Rect boundingRect = Imgproc.boundingRect(contour);
            double area = Imgproc.contourArea(contour);
            double aspectRatio = (double) boundingRect.width / boundingRect.height;
            
            // Check if contour meets custom signature criteria
            if (area >= minArea && area <= maxArea &&
                aspectRatio >= minAspectRatio && aspectRatio <= maxAspectRatio &&
                contour.total() >= MIN_CONTOUR_POINTS) {
                
                signatureRegions.add(boundingRect);
                logger.debug("Custom signature found: area={}, aspect ratio={}", area, aspectRatio);
            }
        }
        
        hierarchy.release();
        return signatureRegions;
    }
    
    /**
     * Checks if a contour is likely to be a signature based on various criteria.
     */
    private static boolean isLikelySignature(Rect boundingRect, double area, 
                                           double aspectRatio, MatOfPoint contour) {
        // Check area constraints
        if (area < MIN_SIGNATURE_AREA || area > MAX_SIGNATURE_AREA) {
            return false;
        }
        
        // Check aspect ratio (signatures are typically wider than tall)
        if (aspectRatio < MIN_ASPECT_RATIO || aspectRatio > MAX_ASPECT_RATIO) {
            return false;
        }
        
        // Check minimum number of contour points (signatures have complex shapes)
        if (contour.total() < MIN_CONTOUR_POINTS) {
            return false;
        }
        
        // Check minimum dimensions
        if (boundingRect.width < 50 || boundingRect.height < 20) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Clamps a rectangle to ensure it's within image bounds.
     */
    private static Rect clampRectToImage(Rect rect, Mat image) {
        int x = Math.max(0, rect.x);
        int y = Math.max(0, rect.y);
        int width = Math.min(rect.width, image.cols() - x);
        int height = Math.min(rect.height, image.rows() - y);
        
        return new Rect(x, y, width, height);
    }
}
