package com.signaturedetection;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for signature detection and line removal functionality.
 */
public class SignatureDetectionTest {
    
    @BeforeAll
    static void setUp() {
        // Load OpenCV
        nu.pattern.OpenCV.loadShared();
    }
    
    @Test
    void testImagePreprocessing() {
        // Create a test image
        Mat testImage = createTestImage(300, 200);
        
        // Preprocess the image
        Mat processed = ImageProcessor.preprocessImage(testImage);
        
        // Verify the processed image is binary
        assertNotNull(processed);
        assertEquals(CvType.CV_8UC1, processed.type());
        
        // Clean up
        testImage.release();
        processed.release();
    }
    
    @Test
    void testLineRemoval() {
        // Create an image with horizontal and vertical lines
        Mat imageWithLines = createImageWithLines(400, 300);
        
        // Remove lines
        Mat imageWithoutLines = LineRemover.removeLines(imageWithLines);
        
        // Verify the result
        assertNotNull(imageWithoutLines);
        assertEquals(imageWithLines.size(), imageWithoutLines.size());
        
        // Clean up
        imageWithLines.release();
        imageWithoutLines.release();
    }
    
    @Test
    void testHorizontalLineRemoval() {
        Mat imageWithHorizontalLines = createImageWithHorizontalLines(300, 200);
        
        Mat result = LineRemover.removeHorizontalLines(imageWithHorizontalLines);
        
        assertNotNull(result);
        assertEquals(imageWithHorizontalLines.size(), result.size());
        
        imageWithHorizontalLines.release();
        result.release();
    }
    
    @Test
    void testVerticalLineRemoval() {
        Mat imageWithVerticalLines = createImageWithVerticalLines(300, 200);
        
        Mat result = LineRemover.removeVerticalLines(imageWithVerticalLines);
        
        assertNotNull(result);
        assertEquals(imageWithVerticalLines.size(), result.size());
        
        imageWithVerticalLines.release();
        result.release();
    }
    
    @Test
    void testSignatureDetection() {
        // Create an image with signature-like regions
        Mat imageWithSignatures = createImageWithSignatures(500, 400);
        
        // Detect signatures
        List<Rect> signatures = SignatureDetector.detectSignatureRegions(imageWithSignatures);
        
        // Should detect at least one signature
        assertNotNull(signatures);
        assertTrue(signatures.size() >= 0); // May be 0 if test image doesn't meet criteria
        
        imageWithSignatures.release();
    }
    
    @Test
    void testSignatureExtraction() {
        Mat originalImage = createTestImage(400, 300);
        
        // Create some test signature regions
        List<Rect> signatureRegions = List.of(
            new Rect(50, 50, 100, 30),
            new Rect(200, 150, 120, 40)
        );
        
        // Extract signatures
        List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);
        
        // Verify extraction
        assertEquals(signatureRegions.size(), extractedSignatures.size());
        
        for (int i = 0; i < extractedSignatures.size(); i++) {
            Mat signature = extractedSignatures.get(i);
            Rect region = signatureRegions.get(i);
            
            assertEquals(region.width, signature.cols());
            assertEquals(region.height, signature.rows());
        }
        
        // Clean up
        originalImage.release();
        for (Mat signature : extractedSignatures) {
            signature.release();
        }
    }
    
    @Test
    void testCustomSignatureDetection() {
        Mat testImage = createImageWithSignatures(400, 300);
        
        // Test with custom parameters
        List<Rect> signatures = SignatureDetector.detectSignatureRegionsCustom(
            testImage, 500, 10000, 2.0, 6.0);
        
        assertNotNull(signatures);
        
        testImage.release();
    }
    
    @Test
    void testVisualization() {
        Mat originalImage = createTestImage(300, 200);
        List<Rect> signatureRegions = List.of(new Rect(50, 50, 100, 30));
        
        Mat visualization = ImageProcessor.visualizeSignatureRegions(originalImage, signatureRegions);
        
        assertNotNull(visualization);
        assertEquals(3, visualization.channels()); // Should be color image
        
        originalImage.release();
        visualization.release();
    }
    
    @Test
    void testImageSaving(@TempDir Path tempDir) throws IOException {
        Mat testImage = createTestImage(100, 100);
        String outputPath = tempDir.resolve("test_output.png").toString();
        
        // Should not throw exception
        assertDoesNotThrow(() -> ImageProcessor.saveImage(testImage, outputPath));
        
        testImage.release();
    }
    
    // Helper methods to create test images
    
    private Mat createTestImage(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add some random content
        for (int i = 0; i < 10; i++) {
            Point pt1 = new Point(Math.random() * width, Math.random() * height);
            Point pt2 = new Point(Math.random() * width, Math.random() * height);
            Imgproc.line(image, pt1, pt2, new Scalar(255), 2);
        }
        
        return image;
    }
    
    private Mat createImageWithLines(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add horizontal lines
        for (int y = 50; y < height; y += 50) {
            Imgproc.line(image, new Point(0, y), new Point(width, y), new Scalar(255), 2);
        }
        
        // Add vertical lines
        for (int x = 50; x < width; x += 50) {
            Imgproc.line(image, new Point(x, 0), new Point(x, height), new Scalar(255), 2);
        }
        
        return image;
    }
    
    private Mat createImageWithHorizontalLines(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add horizontal lines
        for (int y = 30; y < height; y += 40) {
            Imgproc.line(image, new Point(0, y), new Point(width, y), new Scalar(255), 1);
        }
        
        return image;
    }
    
    private Mat createImageWithVerticalLines(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add vertical lines
        for (int x = 30; x < width; x += 40) {
            Imgproc.line(image, new Point(x, 0), new Point(x, height), new Scalar(255), 1);
        }
        
        return image;
    }
    
    private Mat createImageWithSignatures(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Create signature-like regions (irregular shapes)
        Point[] points1 = {
            new Point(50, 100), new Point(150, 95), new Point(180, 110), 
            new Point(160, 125), new Point(70, 120)
        };
        
        Point[] points2 = {
            new Point(250, 200), new Point(350, 195), new Point(380, 210), 
            new Point(360, 225), new Point(270, 220)
        };
        
        // Draw signature-like contours
        for (int i = 0; i < points1.length - 1; i++) {
            Imgproc.line(image, points1[i], points1[i + 1], new Scalar(255), 2);
        }
        
        for (int i = 0; i < points2.length - 1; i++) {
            Imgproc.line(image, points2[i], points2[i + 1], new Scalar(255), 2);
        }
        
        return image;
    }
}
