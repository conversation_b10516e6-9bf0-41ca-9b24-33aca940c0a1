package com.signaturedetection.examples;

import com.signaturedetection.*;
import org.opencv.core.Mat;
import org.opencv.core.Rect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * Example demonstrating how to use custom parameters for signature detection and line removal.
 * This is useful when the default parameters don't work well for your specific document type.
 */
public class CustomParametersExample {
    private static final Logger logger = LoggerFactory.getLogger(CustomParametersExample.class);
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java CustomParametersExample <input-tiff-path>");
            System.out.println("Example: java CustomParametersExample document.tiff");
            return;
        }
        
        String inputPath = args[0];
        String outputDir = "./custom_output/";
        
        try {
            runCustomParametersExample(inputPath, outputDir);
        } catch (Exception e) {
            logger.error("Error in custom parameters example: {}", e.getMessage(), e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Demonstrates signature detection with various custom parameter sets.
     */
    public static void runCustomParametersExample(String inputPath, String outputDir) throws IOException {
        System.out.println("=== Custom Parameters Example ===");
        System.out.println("Input: " + inputPath);
        System.out.println("Output: " + outputDir);
        System.out.println();
        
        // Create output directory
        java.io.File outputDirectory = new java.io.File(outputDir);
        if (!outputDirectory.exists()) {
            outputDirectory.mkdirs();
        }
        
        // Load and preprocess image once
        Mat originalImage = ImageProcessor.loadTiffImage(inputPath);
        Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage);
        
        // Test different parameter combinations
        testDefaultParameters(originalImage, preprocessedImage, outputDir + "/default/");
        testSmallSignatures(originalImage, preprocessedImage, outputDir + "/small_signatures/");
        testLargeSignatures(originalImage, preprocessedImage, outputDir + "/large_signatures/");
        testThickLines(originalImage, preprocessedImage, outputDir + "/thick_lines/");
        testThinLines(originalImage, preprocessedImage, outputDir + "/thin_lines/");
        
        // Clean up
        originalImage.release();
        preprocessedImage.release();
        
        System.out.println("=== Custom Parameters Testing Complete ===");
        System.out.println("Check subdirectories in " + outputDir + " for results");
    }
    
    /**
     * Test with default parameters for comparison.
     */
    private static void testDefaultParameters(Mat originalImage, Mat preprocessedImage, String outputDir) throws IOException {
        System.out.println("Testing default parameters...");
        
        new java.io.File(outputDir).mkdirs();
        
        // Use default line removal
        Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);
        
        // Use default signature detection
        List<Rect> signatures = SignatureDetector.detectSignatureRegions(imageWithoutLines);
        
        saveResults(originalImage, imageWithoutLines, signatures, outputDir, "default");
        
        System.out.println("  Default parameters: " + signatures.size() + " signatures detected");
        imageWithoutLines.release();
    }
    
    /**
     * Test parameters optimized for small signatures.
     */
    private static void testSmallSignatures(Mat originalImage, Mat preprocessedImage, String outputDir) throws IOException {
        System.out.println("Testing small signature parameters...");
        
        new java.io.File(outputDir).mkdirs();
        
        // Use smaller kernels for line removal to preserve small details
        Mat imageWithoutLines = LineRemover.removeLinesCustom(preprocessedImage, 20, 15);
        
        // Use parameters for small signatures
        List<Rect> signatures = SignatureDetector.detectSignatureRegionsCustom(
            imageWithoutLines,
            300,    // Min area (smaller)
            15000,  // Max area
            1.2,    // Min aspect ratio (more square allowed)
            10.0    // Max aspect ratio
        );
        
        saveResults(originalImage, imageWithoutLines, signatures, outputDir, "small");
        
        System.out.println("  Small signature parameters: " + signatures.size() + " signatures detected");
        imageWithoutLines.release();
    }
    
    /**
     * Test parameters optimized for large signatures.
     */
    private static void testLargeSignatures(Mat originalImage, Mat preprocessedImage, String outputDir) throws IOException {
        System.out.println("Testing large signature parameters...");
        
        new java.io.File(outputDir).mkdirs();
        
        // Use larger kernels for line removal
        Mat imageWithoutLines = LineRemover.removeLinesCustom(preprocessedImage, 80, 60);
        
        // Use parameters for large signatures
        List<Rect> signatures = SignatureDetector.detectSignatureRegionsCustom(
            imageWithoutLines,
            2000,   // Min area (larger)
            100000, // Max area (much larger)
            2.0,    // Min aspect ratio
            6.0     // Max aspect ratio (more constrained)
        );
        
        saveResults(originalImage, imageWithoutLines, signatures, outputDir, "large");
        
        System.out.println("  Large signature parameters: " + signatures.size() + " signatures detected");
        imageWithoutLines.release();
    }
    
    /**
     * Test parameters for documents with thick lines.
     */
    private static void testThickLines(Mat originalImage, Mat preprocessedImage, String outputDir) throws IOException {
        System.out.println("Testing thick line removal parameters...");
        
        new java.io.File(outputDir).mkdirs();
        
        // Use larger kernels for thick line removal
        Mat imageWithoutLines = LineRemover.removeLinesCustom(preprocessedImage, 100, 80);
        
        // Use standard signature detection
        List<Rect> signatures = SignatureDetector.detectSignatureRegions(imageWithoutLines);
        
        saveResults(originalImage, imageWithoutLines, signatures, outputDir, "thick_lines");
        
        System.out.println("  Thick line parameters: " + signatures.size() + " signatures detected");
        imageWithoutLines.release();
    }
    
    /**
     * Test parameters for documents with thin lines.
     */
    private static void testThinLines(Mat originalImage, Mat preprocessedImage, String outputDir) throws IOException {
        System.out.println("Testing thin line removal parameters...");
        
        new java.io.File(outputDir).mkdirs();
        
        // Use smaller kernels for thin line removal
        Mat imageWithoutLines = LineRemover.removeLinesCustom(preprocessedImage, 15, 10);
        
        // Use standard signature detection
        List<Rect> signatures = SignatureDetector.detectSignatureRegions(imageWithoutLines);
        
        saveResults(originalImage, imageWithoutLines, signatures, outputDir, "thin_lines");
        
        System.out.println("  Thin line parameters: " + signatures.size() + " signatures detected");
        imageWithoutLines.release();
    }
    
    /**
     * Save results for a parameter test.
     */
    private static void saveResults(Mat originalImage, Mat processedImage, List<Rect> signatures, 
                                  String outputDir, String prefix) throws IOException {
        
        // Save processed image
        ImageProcessor.saveImage(processedImage, outputDir + prefix + "_processed.png");
        
        // Save visualization
        Mat visualization = ImageProcessor.visualizeSignatureRegions(originalImage, signatures);
        ImageProcessor.saveImage(visualization, outputDir + prefix + "_detection.png");
        visualization.release();
        
        // Save individual signatures
        List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatures);
        for (int i = 0; i < extractedSignatures.size(); i++) {
            ImageProcessor.saveImage(extractedSignatures.get(i), 
                                   outputDir + prefix + "_signature_" + (i + 1) + ".png");
        }
        
        // Clean up extracted signatures
        for (Mat signature : extractedSignatures) {
            signature.release();
        }
        
        // Save parameter report
        saveParameterReport(signatures, outputDir, prefix);
    }
    
    /**
     * Save a report for the parameter test.
     */
    private static void saveParameterReport(List<Rect> signatures, String outputDir, String prefix) throws IOException {
        String reportPath = outputDir + prefix + "_report.txt";
        
        try (java.io.PrintWriter writer = new java.io.PrintWriter(reportPath)) {
            writer.println("Parameter Test Report: " + prefix);
            writer.println("=".repeat(30 + prefix.length()));
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println();
            writer.println("Signatures detected: " + signatures.size());
            writer.println();
            
            for (int i = 0; i < signatures.size(); i++) {
                Rect region = signatures.get(i);
                writer.println("Signature " + (i + 1) + ":");
                writer.println("  Position: (" + region.x + ", " + region.y + ")");
                writer.println("  Size: " + region.width + "x" + region.height);
                writer.println("  Area: " + (region.width * region.height));
                writer.println("  Aspect Ratio: " + String.format("%.2f", (double) region.width / region.height));
                writer.println();
            }
        }
    }
}
