package com.signaturedetection;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for noise removal functionality.
 */
public class NoiseRemovalTest {
    
    @BeforeAll
    static void setUp() {
        // Load OpenCV
        nu.pattern.OpenCV.loadShared();
    }
    
    @Test
    void testBasicNoiseRemoval() {
        // Create a test image with noise
        Mat noisyImage = createNoisyTestImage(200, 150);
        
        // Apply noise removal
        Mat denoised = NoiseRemover.removeNoise(noisyImage);
        
        // Verify the result
        assertNotNull(denoised);
        assertEquals(noisyImage.size(), denoised.size());
        assertEquals(noisyImage.type(), denoised.type());
        
        // Clean up
        noisyImage.release();
        denoised.release();
    }
    
    @Test
    void testAdvancedNoiseRemoval() {
        Mat noisyImage = createNoisyTestImage(200, 150);
        NoiseRemover.NoiseRemovalConfig config = NoiseRemover.NoiseRemovalConfig.createAggressiveConfig();
        
        Mat denoised = NoiseRemover.removeNoiseAdvanced(noisyImage, config);
        
        assertNotNull(denoised);
        assertEquals(noisyImage.size(), denoised.size());
        
        noisyImage.release();
        denoised.release();
    }
    
    @Test
    void testSaltPepperNoiseRemoval() {
        Mat imageWithSaltPepper = createSaltPepperNoiseImage(150, 100);
        
        Mat denoised = NoiseRemover.removeSaltPepperNoise(imageWithSaltPepper, 3);
        
        assertNotNull(denoised);
        assertEquals(imageWithSaltPepper.size(), denoised.size());
        
        imageWithSaltPepper.release();
        denoised.release();
    }
    
    @Test
    void testSpeckleNoiseRemoval() {
        Mat imageWithSpeckle = createSpeckleNoiseImage(150, 100);
        
        Mat denoised = NoiseRemover.removeSpeckleNoise(imageWithSpeckle, 3);
        
        assertNotNull(denoised);
        assertEquals(imageWithSpeckle.size(), denoised.size());
        
        imageWithSpeckle.release();
        denoised.release();
    }
    
    @Test
    void testSmallComponentRemoval() {
        Mat imageWithSmallComponents = createImageWithSmallComponents(200, 150);
        NoiseRemover.NoiseRemovalConfig config = new NoiseRemover.NoiseRemovalConfig();
        config.minComponentArea = 10;
        config.maxComponentArea = 100;
        
        Mat cleaned = NoiseRemover.removeSmallComponents(imageWithSmallComponents, config);
        
        assertNotNull(cleaned);
        assertEquals(imageWithSmallComponents.size(), cleaned.size());
        
        imageWithSmallComponents.release();
        cleaned.release();
    }
    
    @Test
    void testScanningArtifactRemoval() {
        Mat imageWithArtifacts = createImageWithScanningArtifacts(200, 150);
        
        Mat cleaned = NoiseRemover.removeScanningArtifacts(imageWithArtifacts);
        
        assertNotNull(cleaned);
        assertEquals(imageWithArtifacts.size(), cleaned.size());
        
        imageWithArtifacts.release();
        cleaned.release();
    }
    
    @Test
    void testNoiseRemovalConfigurations() {
        // Test light configuration
        NoiseRemover.NoiseRemovalConfig lightConfig = NoiseRemover.NoiseRemovalConfig.createLightConfig();
        assertEquals(3, lightConfig.gaussianKernelSize);
        assertEquals(1, lightConfig.morphKernelSize);
        assertEquals(5, lightConfig.minComponentArea);
        
        // Test aggressive configuration
        NoiseRemover.NoiseRemovalConfig aggressiveConfig = NoiseRemover.NoiseRemovalConfig.createAggressiveConfig();
        assertEquals(5, aggressiveConfig.gaussianKernelSize);
        assertEquals(3, aggressiveConfig.morphKernelSize);
        assertEquals(20, aggressiveConfig.minComponentArea);
        
        // Test default configuration
        NoiseRemover.NoiseRemovalConfig defaultConfig = new NoiseRemover.NoiseRemovalConfig();
        assertEquals(3, defaultConfig.gaussianKernelSize);
        assertEquals(2, defaultConfig.morphKernelSize);
        assertEquals(10, defaultConfig.minComponentArea);
    }
    
    @Test
    void testPreprocessingWithNoiseRemoval() {
        Mat testImage = createNoisyTestImage(200, 150);
        
        // Test with default noise removal
        Mat processed1 = ImageProcessor.preprocessImage(testImage);
        assertNotNull(processed1);
        
        // Test with custom noise removal config
        NoiseRemover.NoiseRemovalConfig config = NoiseRemover.NoiseRemovalConfig.createLightConfig();
        Mat processed2 = ImageProcessor.preprocessImage(testImage, config);
        assertNotNull(processed2);
        
        // Test advanced preprocessing
        Mat processed3 = ImageProcessor.preprocessImageAdvanced(testImage);
        assertNotNull(processed3);
        
        // Clean up
        testImage.release();
        processed1.release();
        processed2.release();
        processed3.release();
    }
    
    @Test
    void testNoiseRemovalEffectiveness() {
        // Create image with known noise patterns
        Mat noisyImage = createComplexNoisyImage(300, 200);
        
        // Apply different noise removal techniques
        Mat lightDenoised = NoiseRemover.removeNoise(noisyImage, NoiseRemover.NoiseRemovalConfig.createLightConfig());
        Mat aggressiveDenoised = NoiseRemover.removeNoise(noisyImage, NoiseRemover.NoiseRemovalConfig.createAggressiveConfig());
        
        // Verify that denoising was applied (images should be different)
        Mat diff1 = new Mat();
        Mat diff2 = new Mat();
        Core.absdiff(noisyImage, lightDenoised, diff1);
        Core.absdiff(noisyImage, aggressiveDenoised, diff2);
        
        Scalar sum1 = Core.sumElems(diff1);
        Scalar sum2 = Core.sumElems(diff2);
        
        // There should be differences (indicating denoising occurred)
        assertTrue(sum1.val[0] > 0, "Light denoising should modify the image");
        assertTrue(sum2.val[0] > 0, "Aggressive denoising should modify the image");
        
        // Clean up
        noisyImage.release();
        lightDenoised.release();
        aggressiveDenoised.release();
        diff1.release();
        diff2.release();
    }
    
    // Helper methods to create test images with different types of noise
    
    private Mat createNoisyTestImage(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add some content (rectangles and lines)
        Imgproc.rectangle(image, new Point(50, 50), new Point(150, 100), new Scalar(255), -1);
        Imgproc.line(image, new Point(20, 20), new Point(180, 130), new Scalar(255), 2);
        
        // Add random noise
        Mat noise = new Mat(height, width, CvType.CV_8UC1);
        Core.randu(noise, new Scalar(0), new Scalar(50));
        Core.add(image, noise, image);
        
        noise.release();
        return image;
    }
    
    private Mat createSaltPepperNoiseImage(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add some content
        Imgproc.rectangle(image, new Point(30, 30), new Point(120, 70), new Scalar(255), -1);
        
        // Add salt and pepper noise
        Mat noise = new Mat(height, width, CvType.CV_8UC1);
        Core.randu(noise, new Scalar(0), new Scalar(255));
        
        Mat mask = new Mat();
        Imgproc.threshold(noise, mask, 250, 255, Imgproc.THRESH_BINARY);
        image.setTo(new Scalar(255), mask);
        
        Imgproc.threshold(noise, mask, 5, 255, Imgproc.THRESH_BINARY_INV);
        image.setTo(new Scalar(0), mask);
        
        noise.release();
        mask.release();
        return image;
    }
    
    private Mat createSpeckleNoiseImage(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add some content
        Imgproc.rectangle(image, new Point(40, 40), new Point(110, 80), new Scalar(255), -1);
        
        // Add speckle noise (small random dots)
        for (int i = 0; i < 100; i++) {
            int x = (int)(Math.random() * width);
            int y = (int)(Math.random() * height);
            Imgproc.circle(image, new Point(x, y), 1, new Scalar(255), -1);
        }
        
        return image;
    }
    
    private Mat createImageWithSmallComponents(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add main content
        Imgproc.rectangle(image, new Point(50, 50), new Point(150, 100), new Scalar(255), -1);
        
        // Add small noise components
        for (int i = 0; i < 50; i++) {
            int x = (int)(Math.random() * width);
            int y = (int)(Math.random() * height);
            int size = (int)(Math.random() * 5) + 1;
            Imgproc.rectangle(image, new Point(x, y), new Point(x + size, y + size), new Scalar(255), -1);
        }
        
        return image;
    }
    
    private Mat createImageWithScanningArtifacts(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add main content
        Imgproc.rectangle(image, new Point(60, 60), new Point(140, 90), new Scalar(255), -1);
        
        // Add scanning artifacts (thin lines, dust spots)
        Imgproc.line(image, new Point(0, 25), new Point(width, 25), new Scalar(128), 1);
        Imgproc.line(image, new Point(75, 0), new Point(75, height), new Scalar(128), 1);
        
        // Add dust spots
        for (int i = 0; i < 20; i++) {
            int x = (int)(Math.random() * width);
            int y = (int)(Math.random() * height);
            Imgproc.circle(image, new Point(x, y), 2, new Scalar(200), -1);
        }
        
        return image;
    }
    
    private Mat createComplexNoisyImage(int width, int height) {
        Mat image = Mat.zeros(height, width, CvType.CV_8UC1);
        
        // Add main content (simulating text/signatures)
        Imgproc.rectangle(image, new Point(50, 50), new Point(200, 80), new Scalar(255), -1);
        Imgproc.rectangle(image, new Point(80, 120), new Point(180, 150), new Scalar(255), -1);
        
        // Add multiple types of noise
        // 1. Gaussian noise
        Mat gaussianNoise = new Mat(height, width, CvType.CV_8UC1);
        Core.randn(gaussianNoise, new Scalar(0), new Scalar(20));
        Core.add(image, gaussianNoise, image);
        
        // 2. Salt and pepper
        Mat saltPepper = new Mat(height, width, CvType.CV_8UC1);
        Core.randu(saltPepper, new Scalar(0), new Scalar(255));
        Mat mask = new Mat();
        Imgproc.threshold(saltPepper, mask, 250, 255, Imgproc.THRESH_BINARY);
        image.setTo(new Scalar(255), mask);
        
        // 3. Small artifacts
        for (int i = 0; i < 30; i++) {
            int x = (int)(Math.random() * width);
            int y = (int)(Math.random() * height);
            Imgproc.circle(image, new Point(x, y), 1, new Scalar(255), -1);
        }
        
        gaussianNoise.release();
        saltPepper.release();
        mask.release();
        
        return image;
    }
}
