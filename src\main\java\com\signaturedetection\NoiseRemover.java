package com.signaturedetection;

import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Comprehensive noise removal utility for scanned documents.
 * Handles various types of noise including salt-and-pepper noise, speckle noise,
 * small artifacts, and scanning artifacts while preserving text and signatures.
 */
public class NoiseRemover {
    private static final Logger logger = LoggerFactory.getLogger(NoiseRemover.class);
    
    /**
     * Noise removal configuration parameters.
     */
    public static class NoiseRemovalConfig {
        // Gaussian blur parameters
        public int gaussianKernelSize = 3;
        public double gaussianSigma = 0.8;
        
        // Median filter parameters
        public int medianKernelSize = 3;
        
        // Morphological operations parameters
        public int morphKernelSize = 2;
        public int morphIterations = 1;
        
        // Connected component filtering
        public int minComponentArea = 10;
        public int maxComponentArea = 100;
        
        // Bilateral filter parameters
        public int bilateralD = 9;
        public double bilateralSigmaColor = 75;
        public double bilateralSigmaSpace = 75;
        
        // Non-local means denoising
        public float nlmH = 10;
        public int nlmTemplateWindowSize = 7;
        public int nlmSearchWindowSize = 21;
        
        public NoiseRemovalConfig() {}
        
        public static NoiseRemovalConfig createLightConfig() {
            NoiseRemovalConfig config = new NoiseRemovalConfig();
            config.gaussianKernelSize = 3;
            config.medianKernelSize = 3;
            config.morphKernelSize = 1;
            config.minComponentArea = 5;
            config.maxComponentArea = 50;
            return config;
        }
        
        public static NoiseRemovalConfig createAggressiveConfig() {
            NoiseRemovalConfig config = new NoiseRemovalConfig();
            config.gaussianKernelSize = 5;
            config.medianKernelSize = 5;
            config.morphKernelSize = 3;
            config.morphIterations = 2;
            config.minComponentArea = 20;
            config.maxComponentArea = 200;
            return config;
        }
    }
    
    /**
     * Comprehensive noise removal with default parameters.
     * 
     * @param inputImage The input grayscale image
     * @return Denoised image
     */
    public static Mat removeNoise(Mat inputImage) {
        return removeNoise(inputImage, new NoiseRemovalConfig());
    }
    
    /**
     * Comprehensive noise removal with custom configuration.
     * 
     * @param inputImage The input grayscale image
     * @param config Noise removal configuration
     * @return Denoised image
     */
    public static Mat removeNoise(Mat inputImage, NoiseRemovalConfig config) {
        logger.info("Starting comprehensive noise removal");
        
        Mat result = inputImage.clone();
        
        // Step 1: Gaussian blur for general noise reduction
        result = applyGaussianDenoising(result, config);
        
        // Step 2: Median filter for salt-and-pepper noise
        result = applyMedianFilter(result, config);
        
        // Step 3: Morphological operations for small artifacts
        result = applyMorphologicalDenoising(result, config);
        
        logger.info("Comprehensive noise removal completed");
        return result;
    }
    
    /**
     * Advanced noise removal using multiple techniques.
     * 
     * @param inputImage The input grayscale image
     * @param config Noise removal configuration
     * @return Denoised image
     */
    public static Mat removeNoiseAdvanced(Mat inputImage, NoiseRemovalConfig config) {
        logger.info("Starting advanced noise removal");
        
        Mat result = inputImage.clone();
        
        // Step 1: Non-local means denoising for texture preservation
        result = applyNonLocalMeansDenoising(result, config);
        
        // Step 2: Bilateral filter for edge preservation
        result = applyBilateralFilter(result, config);
        
        // Step 3: Connected component filtering
        result = removeSmallComponents(result, config);
        
        // Step 4: Morphological cleaning
        result = applyMorphologicalDenoising(result, config);
        
        logger.info("Advanced noise removal completed");
        return result;
    }
    
    /**
     * Removes salt-and-pepper noise using median filtering.
     */
    public static Mat removeSaltPepperNoise(Mat inputImage, int kernelSize) {
        logger.debug("Removing salt-and-pepper noise with kernel size: {}", kernelSize);
        
        Mat result = new Mat();
        Imgproc.medianBlur(inputImage, result, kernelSize);
        
        return result;
    }
    
    /**
     * Removes speckle noise using morphological operations.
     */
    public static Mat removeSpeckleNoise(Mat inputImage, int kernelSize) {
        logger.debug("Removing speckle noise with kernel size: {}", kernelSize);
        
        Mat kernel = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(kernelSize, kernelSize));
        Mat result = new Mat();
        
        // Opening operation to remove small noise
        Imgproc.morphologyEx(inputImage, result, Imgproc.MORPH_OPEN, kernel);
        
        kernel.release();
        return result;
    }
    
    /**
     * Removes small isolated components (noise artifacts).
     */
    public static Mat removeSmallComponents(Mat inputImage, NoiseRemovalConfig config) {
        logger.debug("Removing small components: area range [{}, {}]", 
                    config.minComponentArea, config.maxComponentArea);
        
        // Convert to binary if not already
        Mat binary = new Mat();
        if (inputImage.channels() == 1) {
            // Assume it's already binary or grayscale
            Imgproc.threshold(inputImage, binary, 127, 255, Imgproc.THRESH_BINARY);
        } else {
            Imgproc.cvtColor(inputImage, binary, Imgproc.COLOR_BGR2GRAY);
            Imgproc.threshold(binary, binary, 127, 255, Imgproc.THRESH_BINARY);
        }
        
        // Find connected components
        Mat labels = new Mat();
        Mat stats = new Mat();
        Mat centroids = new Mat();
        int numComponents = Imgproc.connectedComponentsWithStats(binary, labels, stats, centroids);
        
        // Create mask for components to keep
        Mat mask = Mat.zeros(binary.size(), CvType.CV_8UC1);
        
        for (int i = 1; i < numComponents; i++) { // Skip background (component 0)
            double[] statsRow = stats.get(i, 0);
            double area = statsRow[4]; // CC_STAT_AREA
            
            // Keep components that are not too small or too large (likely noise)
            if (area < config.minComponentArea || area > config.maxComponentArea) {
                continue; // Skip this component (remove it)
            }
            
            // Add this component to the mask
            Mat componentMask = new Mat();
            Core.compare(labels, new Scalar(i), componentMask, Core.CMP_EQ);
            Core.bitwise_or(mask, componentMask, mask);
            componentMask.release();
        }
        
        // Apply mask to original image
        Mat result = new Mat();
        inputImage.copyTo(result, mask);
        
        // Clean up
        binary.release();
        labels.release();
        stats.release();
        centroids.release();
        mask.release();
        
        return result;
    }
    
    /**
     * Applies Gaussian denoising.
     */
    private static Mat applyGaussianDenoising(Mat inputImage, NoiseRemovalConfig config) {
        logger.debug("Applying Gaussian denoising: kernel={}, sigma={}", 
                    config.gaussianKernelSize, config.gaussianSigma);
        
        Mat result = new Mat();
        Size kernelSize = new Size(config.gaussianKernelSize, config.gaussianKernelSize);
        Imgproc.GaussianBlur(inputImage, result, kernelSize, config.gaussianSigma);
        
        return result;
    }
    
    /**
     * Applies median filtering.
     */
    private static Mat applyMedianFilter(Mat inputImage, NoiseRemovalConfig config) {
        logger.debug("Applying median filter: kernel={}", config.medianKernelSize);
        
        Mat result = new Mat();
        Imgproc.medianBlur(inputImage, result, config.medianKernelSize);
        
        return result;
    }
    
    /**
     * Applies morphological denoising operations.
     */
    private static Mat applyMorphologicalDenoising(Mat inputImage, NoiseRemovalConfig config) {
        logger.debug("Applying morphological denoising: kernel={}, iterations={}", 
                    config.morphKernelSize, config.morphIterations);
        
        Mat kernel = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, 
                                                  new Size(config.morphKernelSize, config.morphKernelSize));
        Mat result = new Mat();
        
        // Opening operation (erosion followed by dilation) to remove small noise
        Imgproc.morphologyEx(inputImage, result, Imgproc.MORPH_OPEN, kernel, 
                           new Point(-1, -1), config.morphIterations);
        
        kernel.release();
        return result;
    }
    
    /**
     * Applies bilateral filtering for edge-preserving denoising.
     */
    private static Mat applyBilateralFilter(Mat inputImage, NoiseRemovalConfig config) {
        logger.debug("Applying bilateral filter: d={}, sigmaColor={}, sigmaSpace={}", 
                    config.bilateralD, config.bilateralSigmaColor, config.bilateralSigmaSpace);
        
        Mat result = new Mat();
        Imgproc.bilateralFilter(inputImage, result, config.bilateralD, 
                               config.bilateralSigmaColor, config.bilateralSigmaSpace);
        
        return result;
    }
    
    /**
     * Applies non-local means denoising.
     */
    private static Mat applyNonLocalMeansDenoising(Mat inputImage, NoiseRemovalConfig config) {
        logger.debug("Applying non-local means denoising: h={}, templateWindow={}, searchWindow={}", 
                    config.nlmH, config.nlmTemplateWindowSize, config.nlmSearchWindowSize);
        
        Mat result = new Mat();
        
        // Check if image is grayscale or color
        if (inputImage.channels() == 1) {
            org.opencv.photo.Photo.fastNlMeansDenoising(inputImage, result, config.nlmH, 
                                                       config.nlmTemplateWindowSize, config.nlmSearchWindowSize);
        } else {
            org.opencv.photo.Photo.fastNlMeansDenoisingColored(inputImage, result, config.nlmH, config.nlmH,
                                                              config.nlmTemplateWindowSize, config.nlmSearchWindowSize);
        }
        
        return result;
    }
    
    /**
     * Removes scanning artifacts like dust, scratches, and scanner noise.
     */
    public static Mat removeScanningArtifacts(Mat inputImage) {
        logger.info("Removing scanning artifacts");
        
        Mat result = inputImage.clone();
        
        // Remove small dots and specks
        Mat kernel = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(2, 2));
        Imgproc.morphologyEx(result, result, Imgproc.MORPH_OPEN, kernel);
        
        // Remove thin lines that might be scanning artifacts
        Mat horizontalKernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(5, 1));
        Mat verticalKernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(1, 5));
        
        Mat temp = new Mat();
        Imgproc.morphologyEx(result, temp, Imgproc.MORPH_OPEN, horizontalKernel);
        Imgproc.morphologyEx(temp, temp, Imgproc.MORPH_OPEN, verticalKernel);
        
        // Combine with original to preserve important content
        Core.bitwise_or(result, temp, result);
        
        kernel.release();
        horizontalKernel.release();
        verticalKernel.release();
        temp.release();
        
        logger.info("Scanning artifacts removal completed");
        return result;
    }
}
