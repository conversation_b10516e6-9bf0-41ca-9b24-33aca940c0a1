# Signature Detection and Line Removal

A Java application for detecting and extracting signatures from TIFF images while removing unwanted lines from scanned documents.

## Features

- **TIFF Image Support**: Load and process TIFF images using both OpenCV and Java ImageIO
- **Line Removal**: Remove horizontal and vertical lines from scanned documents using morphological operations
- **Signature Detection**: Detect signature regions using contour analysis and connected component analysis
- **Signature Extraction**: Extract individual signatures as separate images
- **Customizable Parameters**: Fine-tune detection parameters for different document types
- **Comprehensive Output**: Generate processed images, extracted signatures, and detection reports

## Requirements

- Java 11 or higher
- Maven 3.6 or higher

## Dependencies

- OpenCV for Java (4.9.0)
- TwelveMonkeys ImageIO for enhanced TIFF support
- Apache Commons IO
- SLF4J with Logback for logging
- JUnit 5 for testing

## Installation

1. Clone or download the project
2. Build the project using Maven:

```bash
mvn clean compile
```

## Usage

### Basic Usage

Process a TIFF image with default parameters:

```bash
mvn exec:java -Dexec.args="path/to/your/document.tiff ./output/"
```

### Programmatic Usage

```java
import com.signaturedetection.SignatureDetectionApp;

// Basic processing
SignatureDetectionApp.processDocument("document.tiff", "./output/");

// Custom processing with fine-tuned parameters
SignatureDetectionApp.processDocumentCustom(
    "document.tiff",           // Input path
    "./output/",               // Output directory
    50,                        // Horizontal kernel size for line removal
    30,                        // Vertical kernel size for line removal
    1000,                      // Minimum signature area
    50000,                     // Maximum signature area
    1.5,                       // Minimum aspect ratio
    8.0                        // Maximum aspect ratio
);
```

### Individual Components

```java
import com.signaturedetection.*;
import org.opencv.core.Mat;
import org.opencv.core.Rect;
import java.util.List;

// Load and preprocess image
Mat originalImage = ImageProcessor.loadTiffImage("document.tiff");
Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage);

// Remove lines
Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);

// Detect signatures
List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);

// Extract signatures
List<Mat> signatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);

// Save results
ImageProcessor.saveImage(imageWithoutLines, "processed.png");
for (int i = 0; i < signatures.size(); i++) {
    ImageProcessor.saveImage(signatures.get(i), "signature_" + (i + 1) + ".png");
}
```

## Output Files

The application generates several output files:

- `processed_image.png`: The original image with lines removed
- `signature_detection_result.png`: Visualization showing detected signature regions highlighted in green
- `signature_1.png`, `signature_2.png`, etc.: Individual extracted signatures
- `detection_report.txt`: Text report with detection statistics and signature details

## Algorithm Details

### Line Removal

1. **Horizontal Line Detection**: Uses morphological operations with horizontal kernels to detect and remove horizontal lines
2. **Vertical Line Detection**: Uses morphological operations with vertical kernels to detect and remove vertical lines
3. **Preservation**: Carefully designed to preserve text and signature content while removing unwanted lines

### Signature Detection

1. **Preprocessing**: Applies Gaussian blur, adaptive thresholding, and morphological operations
2. **Contour Analysis**: Finds contours in the processed image
3. **Feature Filtering**: Filters contours based on:
   - Area (1000-50000 pixels by default)
   - Aspect ratio (1.5-8.0 by default)
   - Complexity (minimum 50 contour points)
   - Minimum dimensions (50x20 pixels)

### Customization Parameters

- **Kernel Sizes**: Adjust horizontal and vertical kernel sizes for line removal based on line thickness
- **Area Constraints**: Set minimum and maximum signature areas based on expected signature sizes
- **Aspect Ratio**: Configure aspect ratio range based on signature characteristics
- **Morphological Operations**: Fine-tune preprocessing parameters for different image qualities

## Testing

Run the test suite:

```bash
mvn test
```

The tests cover:
- Image preprocessing functionality
- Line removal algorithms
- Signature detection and extraction
- Custom parameter handling
- File I/O operations

## Troubleshooting

### Common Issues

1. **OpenCV Loading Issues**: Ensure the OpenCV native library is properly loaded
2. **TIFF Format Issues**: The application uses TwelveMonkeys ImageIO as a fallback for complex TIFF formats
3. **Memory Issues**: Large images may require increased JVM heap size: `-Xmx2g`
4. **No Signatures Detected**: Try adjusting detection parameters for your specific document type

### Performance Tips

- Use appropriate image resolution (300 DPI is typically sufficient)
- Consider preprocessing images to remove noise before processing
- Adjust kernel sizes based on the thickness of lines in your documents
- Fine-tune signature detection parameters based on your signature characteristics

## License

This project is provided as-is for educational and development purposes.
