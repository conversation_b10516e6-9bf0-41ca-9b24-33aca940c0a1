package com.signaturedetection;

import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * Core image processing utilities for TIFF image handling and preprocessing.
 * Handles TIFF loading, conversion to OpenCV Mat, and basic preprocessing operations.
 */
public class ImageProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ImageProcessor.class);
    
    static {
        // Load OpenCV native library
        nu.pattern.OpenCV.loadShared();
        logger.info("OpenCV loaded successfully");
    }
    
    /**
     * Loads a TIFF image and converts it to OpenCV Mat format.
     * 
     * @param imagePath Path to the TIFF image file
     * @return Mat representation of the image
     * @throws IOException if the image cannot be loaded
     */
    public static Mat loadTiffImage(String imagePath) throws IOException {
        logger.info("Loading TIFF image: {}", imagePath);
        
        // First try with OpenCV
        Mat image = Imgcodecs.imread(imagePath, Imgcodecs.IMREAD_GRAYSCALE);
        
        if (image.empty()) {
            // Fallback to Java ImageIO for better TIFF support
            logger.debug("OpenCV failed to load image, trying ImageIO");
            BufferedImage bufferedImage = ImageIO.read(new File(imagePath));
            
            if (bufferedImage == null) {
                throw new IOException("Failed to load image: " + imagePath);
            }
            
            image = bufferedImageToMat(bufferedImage);
        }
        
        logger.info("Image loaded successfully: {}x{}", image.cols(), image.rows());
        return image;
    }
    
    /**
     * Converts a BufferedImage to OpenCV Mat.
     * 
     * @param bufferedImage The BufferedImage to convert
     * @return Mat representation
     */
    public static Mat bufferedImageToMat(BufferedImage bufferedImage) {
        // Convert to grayscale if needed
        BufferedImage grayImage;
        if (bufferedImage.getType() != BufferedImage.TYPE_BYTE_GRAY) {
            grayImage = new BufferedImage(bufferedImage.getWidth(), bufferedImage.getHeight(), 
                                        BufferedImage.TYPE_BYTE_GRAY);
            grayImage.getGraphics().drawImage(bufferedImage, 0, 0, null);
        } else {
            grayImage = bufferedImage;
        }
        
        // Extract pixel data
        byte[] pixels = new byte[grayImage.getWidth() * grayImage.getHeight()];
        grayImage.getRaster().getDataElements(0, 0, grayImage.getWidth(), grayImage.getHeight(), pixels);
        
        // Create Mat
        Mat mat = new Mat(grayImage.getHeight(), grayImage.getWidth(), CvType.CV_8UC1);
        mat.put(0, 0, pixels);
        
        return mat;
    }
    
    /**
     * Converts OpenCV Mat to BufferedImage.
     *
     * @param mat The Mat to convert
     * @return BufferedImage representation
     */
    public static BufferedImage matToBufferedImage(Mat mat) {
        int type = BufferedImage.TYPE_BYTE_GRAY;
        if (mat.channels() > 1) {
            type = BufferedImage.TYPE_3BYTE_BGR;
        }

        int bufferSize = mat.channels() * mat.cols() * mat.rows();
        byte[] buffer = new byte[bufferSize];
        mat.get(0, 0, buffer); // get all pixels

        BufferedImage image = new BufferedImage(mat.cols(), mat.rows(), type);
        final byte[] targetPixels = ((java.awt.image.DataBufferByte) image.getRaster().getDataBuffer()).getData();
        System.arraycopy(buffer, 0, targetPixels, 0, buffer.length);

        return image;
    }
    
    /**
     * Preprocesses the image for signature detection and line removal.
     * Applies comprehensive noise reduction, binarization, and morphological operations.
     *
     * @param inputImage The input grayscale image
     * @return Preprocessed binary image
     */
    public static Mat preprocessImage(Mat inputImage) {
        return preprocessImage(inputImage, NoiseRemover.NoiseRemovalConfig.createLightConfig());
    }

    /**
     * Preprocesses the image with custom noise removal configuration.
     *
     * @param inputImage The input grayscale image
     * @param noiseConfig Configuration for noise removal
     * @return Preprocessed binary image
     */
    public static Mat preprocessImage(Mat inputImage, NoiseRemover.NoiseRemovalConfig noiseConfig) {
        logger.info("Preprocessing image for signature detection with noise removal");

        // Step 1: Remove noise from the original image
        Mat denoised = NoiseRemover.removeNoise(inputImage, noiseConfig);

        // Step 2: Apply additional Gaussian blur for smoothing
        Mat processed = new Mat();
        Imgproc.GaussianBlur(denoised, processed, new Size(3, 3), 0);

        // Step 3: Apply adaptive threshold for better binarization
        Mat binary = new Mat();
        Imgproc.adaptiveThreshold(processed, binary, 255,
                                Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                                Imgproc.THRESH_BINARY, 11, 2);

        // Step 4: Invert the image (make text/signatures black on white background)
        Core.bitwise_not(binary, binary);

        // Step 5: Apply morphological opening to remove remaining small noise
        Mat kernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, new Size(2, 2));
        Imgproc.morphologyEx(binary, binary, Imgproc.MORPH_OPEN, kernel);

        // Clean up intermediate results
        denoised.release();
        processed.release();
        kernel.release();

        logger.info("Image preprocessing with noise removal completed");
        return binary;
    }

    /**
     * Advanced preprocessing with comprehensive noise removal.
     * Uses more aggressive noise removal techniques.
     *
     * @param inputImage The input grayscale image
     * @return Preprocessed binary image
     */
    public static Mat preprocessImageAdvanced(Mat inputImage) {
        logger.info("Advanced preprocessing with comprehensive noise removal");

        // Step 1: Remove scanning artifacts
        Mat cleanImage = NoiseRemover.removeScanningArtifacts(inputImage);

        // Step 2: Apply advanced noise removal
        NoiseRemover.NoiseRemovalConfig aggressiveConfig = NoiseRemover.NoiseRemovalConfig.createAggressiveConfig();
        Mat denoised = NoiseRemover.removeNoiseAdvanced(cleanImage, aggressiveConfig);

        // Step 3: Apply adaptive threshold
        Mat binary = new Mat();
        Imgproc.adaptiveThreshold(denoised, binary, 255,
                                Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                                Imgproc.THRESH_BINARY, 11, 2);

        // Step 4: Invert the image
        Core.bitwise_not(binary, binary);

        // Step 5: Final morphological cleaning
        Mat kernel = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(3, 3));
        Imgproc.morphologyEx(binary, binary, Imgproc.MORPH_OPEN, kernel);

        // Clean up
        cleanImage.release();
        denoised.release();
        kernel.release();

        logger.info("Advanced preprocessing completed");
        return binary;
    }
    
    /**
     * Saves a Mat image to file.
     * 
     * @param image The Mat image to save
     * @param outputPath The output file path
     * @throws IOException if the image cannot be saved
     */
    public static void saveImage(Mat image, String outputPath) throws IOException {
        logger.info("Saving image to: {}", outputPath);
        
        if (!Imgcodecs.imwrite(outputPath, image)) {
            // Fallback to ImageIO
            BufferedImage bufferedImage = matToBufferedImage(image);
            String format = outputPath.substring(outputPath.lastIndexOf('.') + 1);
            ImageIO.write(bufferedImage, format, new File(outputPath));
        }
        
        logger.info("Image saved successfully");
    }
    
    /**
     * Creates a visualization image showing detected signature regions.
     * 
     * @param originalImage The original image
     * @param signatureRegions List of detected signature regions
     * @return Mat with signature regions highlighted
     */
    public static Mat visualizeSignatureRegions(Mat originalImage, java.util.List<Rect> signatureRegions) {
        Mat visualization = new Mat();
        
        // Convert to color if grayscale
        if (originalImage.channels() == 1) {
            Imgproc.cvtColor(originalImage, visualization, Imgproc.COLOR_GRAY2BGR);
        } else {
            visualization = originalImage.clone();
        }
        
        // Draw rectangles around detected signatures
        Scalar color = new Scalar(0, 255, 0); // Green color
        for (Rect region : signatureRegions) {
            Imgproc.rectangle(visualization, region.tl(), region.br(), color, 2);
        }
        
        return visualization;
    }
}
