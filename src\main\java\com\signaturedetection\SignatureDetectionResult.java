package com.signaturedetection;

import org.opencv.core.Rect;

import java.util.List;
import java.util.ArrayList;

/**
 * Result object containing all signature detection and line removal results.
 * This class provides structured access to detection results including coordinates,
 * statistics, and processing information for testing and validation purposes.
 */
public class SignatureDetectionResult {
    
    /**
     * Represents a detected signature with its properties.
     */
    public static class DetectedSignature {
        private final int x;
        private final int y;
        private final int width;
        private final int height;
        private final double area;
        private final double aspectRatio;
        private final int id;
        
        public DetectedSignature(int id, Rect rect) {
            this.id = id;
            this.x = rect.x;
            this.y = rect.y;
            this.width = rect.width;
            this.height = rect.height;
            this.area = rect.width * rect.height;
            this.aspectRatio = (double) rect.width / rect.height;
        }
        
        // Getters
        public int getId() { return id; }
        public int getX() { return x; }
        public int getY() { return y; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
        public double getArea() { return area; }
        public double getAspectRatio() { return aspectRatio; }
        
        /**
         * Get the top-left corner coordinates.
         */
        public Point getTopLeft() {
            return new Point(x, y);
        }
        
        /**
         * Get the bottom-right corner coordinates.
         */
        public Point getBottomRight() {
            return new Point(x + width, y + height);
        }
        
        /**
         * Get the center coordinates.
         */
        public Point getCenter() {
            return new Point(x + width / 2, y + height / 2);
        }
        
        /**
         * Check if this signature overlaps with another signature.
         */
        public boolean overlaps(DetectedSignature other) {
            return !(x + width <= other.x || other.x + other.width <= x ||
                    y + height <= other.y || other.y + other.height <= y);
        }
        
        /**
         * Calculate the distance from this signature's center to another signature's center.
         */
        public double distanceTo(DetectedSignature other) {
            Point thisCenter = getCenter();
            Point otherCenter = other.getCenter();
            double dx = thisCenter.x - otherCenter.x;
            double dy = thisCenter.y - otherCenter.y;
            return Math.sqrt(dx * dx + dy * dy);
        }
        
        @Override
        public String toString() {
            return String.format("Signature[id=%d, pos=(%d,%d), size=%dx%d, area=%.0f, ratio=%.2f]",
                    id, x, y, width, height, area, aspectRatio);
        }
    }
    
    /**
     * Simple point class for coordinates.
     */
    public static class Point {
        public final int x;
        public final int y;
        
        public Point(int x, int y) {
            this.x = x;
            this.y = y;
        }
        
        @Override
        public String toString() {
            return String.format("(%d,%d)", x, y);
        }
    }
    
    /**
     * Processing statistics and metadata.
     */
    public static class ProcessingStats {
        private final long processingTimeMs;
        private final int imageWidth;
        private final int imageHeight;
        private final int totalContours;
        private final boolean linesRemoved;
        private final String inputPath;
        
        public ProcessingStats(long processingTimeMs, int imageWidth, int imageHeight, 
                             int totalContours, boolean linesRemoved, String inputPath) {
            this.processingTimeMs = processingTimeMs;
            this.imageWidth = imageWidth;
            this.imageHeight = imageHeight;
            this.totalContours = totalContours;
            this.linesRemoved = linesRemoved;
            this.inputPath = inputPath;
        }
        
        // Getters
        public long getProcessingTimeMs() { return processingTimeMs; }
        public int getImageWidth() { return imageWidth; }
        public int getImageHeight() { return imageHeight; }
        public int getTotalContours() { return totalContours; }
        public boolean isLinesRemoved() { return linesRemoved; }
        public String getInputPath() { return inputPath; }
    }
    
    // Main result data
    private final List<DetectedSignature> signatures;
    private final ProcessingStats stats;
    private final boolean success;
    private final String errorMessage;
    
    /**
     * Constructor for successful results.
     */
    public SignatureDetectionResult(List<Rect> signatureRegions, ProcessingStats stats) {
        this.signatures = new ArrayList<>();
        for (int i = 0; i < signatureRegions.size(); i++) {
            this.signatures.add(new DetectedSignature(i + 1, signatureRegions.get(i)));
        }
        this.stats = stats;
        this.success = true;
        this.errorMessage = null;
    }
    
    /**
     * Constructor for failed results.
     */
    public SignatureDetectionResult(String errorMessage) {
        this.signatures = new ArrayList<>();
        this.stats = null;
        this.success = false;
        this.errorMessage = errorMessage;
    }
    
    // Getters
    public List<DetectedSignature> getSignatures() { return new ArrayList<>(signatures); }
    public ProcessingStats getStats() { return stats; }
    public boolean isSuccess() { return success; }
    public String getErrorMessage() { return errorMessage; }
    public int getSignatureCount() { return signatures.size(); }
    
    /**
     * Get signature by ID.
     */
    public DetectedSignature getSignatureById(int id) {
        return signatures.stream()
                .filter(sig -> sig.getId() == id)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Get signatures within a specific area.
     */
    public List<DetectedSignature> getSignaturesInArea(int x, int y, int width, int height) {
        List<DetectedSignature> result = new ArrayList<>();
        for (DetectedSignature sig : signatures) {
            if (sig.getX() >= x && sig.getY() >= y && 
                sig.getX() + sig.getWidth() <= x + width && 
                sig.getY() + sig.getHeight() <= y + height) {
                result.add(sig);
            }
        }
        return result;
    }
    
    /**
     * Get signatures larger than a minimum area.
     */
    public List<DetectedSignature> getSignaturesLargerThan(double minArea) {
        return signatures.stream()
                .filter(sig -> sig.getArea() >= minArea)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Get signatures with aspect ratio in a specific range.
     */
    public List<DetectedSignature> getSignaturesByAspectRatio(double minRatio, double maxRatio) {
        return signatures.stream()
                .filter(sig -> sig.getAspectRatio() >= minRatio && sig.getAspectRatio() <= maxRatio)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Convert to JSON-like string for easy testing and debugging.
     */
    public String toJsonString() {
        StringBuilder sb = new StringBuilder();
        sb.append("{\n");
        sb.append("  \"success\": ").append(success).append(",\n");
        sb.append("  \"signatureCount\": ").append(getSignatureCount()).append(",\n");
        
        if (success && stats != null) {
            sb.append("  \"processingTimeMs\": ").append(stats.getProcessingTimeMs()).append(",\n");
            sb.append("  \"imageSize\": \"").append(stats.getImageWidth()).append("x").append(stats.getImageHeight()).append("\",\n");
            sb.append("  \"totalContours\": ").append(stats.getTotalContours()).append(",\n");
            sb.append("  \"linesRemoved\": ").append(stats.isLinesRemoved()).append(",\n");
        }
        
        if (!success) {
            sb.append("  \"error\": \"").append(errorMessage).append("\",\n");
        }
        
        sb.append("  \"signatures\": [\n");
        for (int i = 0; i < signatures.size(); i++) {
            DetectedSignature sig = signatures.get(i);
            sb.append("    {\n");
            sb.append("      \"id\": ").append(sig.getId()).append(",\n");
            sb.append("      \"x\": ").append(sig.getX()).append(",\n");
            sb.append("      \"y\": ").append(sig.getY()).append(",\n");
            sb.append("      \"width\": ").append(sig.getWidth()).append(",\n");
            sb.append("      \"height\": ").append(sig.getHeight()).append(",\n");
            sb.append("      \"area\": ").append(sig.getArea()).append(",\n");
            sb.append("      \"aspectRatio\": ").append(String.format("%.2f", sig.getAspectRatio())).append("\n");
            sb.append("    }");
            if (i < signatures.size() - 1) sb.append(",");
            sb.append("\n");
        }
        sb.append("  ]\n");
        sb.append("}");
        return sb.toString();
    }
    
    @Override
    public String toString() {
        if (!success) {
            return "SignatureDetectionResult[FAILED: " + errorMessage + "]";
        }
        return String.format("SignatureDetectionResult[success=%s, signatures=%d, processingTime=%dms]",
                success, getSignatureCount(), stats != null ? stats.getProcessingTimeMs() : 0);
    }
}
