package com.signaturedetection.examples;

import com.signaturedetection.*;
import org.opencv.core.Mat;
import org.opencv.core.Rect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * Basic example demonstrating signature detection and line removal.
 * This example shows the step-by-step process of processing a TIFF document.
 */
public class BasicExample {
    private static final Logger logger = LoggerFactory.getLogger(BasicExample.class);
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java BasicExample <input-tiff-path>");
            System.out.println("Example: java BasicExample document.tiff");
            return;
        }
        
        String inputPath = args[0];
        String outputDir = "./example_output/";
        
        try {
            runBasicExample(inputPath, outputDir);
        } catch (Exception e) {
            logger.error("Error in basic example: {}", e.getMessage(), e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Demonstrates the basic signature detection workflow.
     */
    public static void runBasicExample(String inputPath, String outputDir) throws IOException {
        System.out.println("=== Basic Signature Detection Example ===");
        System.out.println("Input: " + inputPath);
        System.out.println("Output: " + outputDir);
        System.out.println();
        
        // Create output directory
        java.io.File outputDirectory = new java.io.File(outputDir);
        if (!outputDirectory.exists()) {
            outputDirectory.mkdirs();
            System.out.println("Created output directory: " + outputDir);
        }
        
        // Step 1: Load the TIFF image
        System.out.println("Step 1: Loading TIFF image...");
        Mat originalImage = ImageProcessor.loadTiffImage(inputPath);
        System.out.println("  Image size: " + originalImage.cols() + "x" + originalImage.rows());
        
        // Step 2: Preprocess the image
        System.out.println("Step 2: Preprocessing image...");
        Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage);
        System.out.println("  Applied noise reduction and binarization");
        
        // Save preprocessed image for inspection
        String preprocessedPath = outputDir + "/01_preprocessed.png";
        ImageProcessor.saveImage(preprocessedImage, preprocessedPath);
        System.out.println("  Saved: " + preprocessedPath);
        
        // Step 3: Remove lines
        System.out.println("Step 3: Removing lines...");
        Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);
        System.out.println("  Removed horizontal and vertical lines");
        
        // Save image without lines
        String noLinesPath = outputDir + "/02_lines_removed.png";
        ImageProcessor.saveImage(imageWithoutLines, noLinesPath);
        System.out.println("  Saved: " + noLinesPath);
        
        // Step 4: Detect signatures
        System.out.println("Step 4: Detecting signatures...");
        List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);
        System.out.println("  Found " + signatureRegions.size() + " potential signatures");
        
        // Print signature details
        for (int i = 0; i < signatureRegions.size(); i++) {
            Rect region = signatureRegions.get(i);
            System.out.println("    Signature " + (i + 1) + ": " + 
                             region.width + "x" + region.height + 
                             " at (" + region.x + ", " + region.y + ")");
        }
        
        // Step 5: Create visualization
        System.out.println("Step 5: Creating visualization...");
        Mat visualization = ImageProcessor.visualizeSignatureRegions(originalImage, signatureRegions);
        String visualizationPath = outputDir + "/03_signature_detection.png";
        ImageProcessor.saveImage(visualization, visualizationPath);
        System.out.println("  Saved: " + visualizationPath);
        
        // Step 6: Extract signatures
        System.out.println("Step 6: Extracting signatures...");
        List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);
        
        for (int i = 0; i < extractedSignatures.size(); i++) {
            String signaturePath = outputDir + "/04_signature_" + (i + 1) + ".png";
            ImageProcessor.saveImage(extractedSignatures.get(i), signaturePath);
            System.out.println("  Saved: " + signaturePath);
        }
        
        // Step 7: Generate report
        System.out.println("Step 7: Generating report...");
        generateDetailedReport(signatureRegions, outputDir);
        
        // Clean up memory
        originalImage.release();
        preprocessedImage.release();
        imageWithoutLines.release();
        visualization.release();
        for (Mat signature : extractedSignatures) {
            signature.release();
        }
        
        System.out.println();
        System.out.println("=== Processing Complete ===");
        System.out.println("Check the output directory for results: " + outputDir);
    }
    
    /**
     * Generates a detailed report of the processing results.
     */
    private static void generateDetailedReport(List<Rect> signatureRegions, String outputDir) throws IOException {
        String reportPath = outputDir + "/detailed_report.txt";
        
        try (java.io.PrintWriter writer = new java.io.PrintWriter(reportPath)) {
            writer.println("Signature Detection - Detailed Report");
            writer.println("====================================");
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println();
            
            writer.println("Processing Summary:");
            writer.println("- Total signatures detected: " + signatureRegions.size());
            writer.println("- Processing steps completed: 7");
            writer.println();
            
            if (signatureRegions.isEmpty()) {
                writer.println("No signatures were detected in this document.");
                writer.println("This could be due to:");
                writer.println("- No signatures present in the image");
                writer.println("- Signatures too small or too large for default parameters");
                writer.println("- Image quality issues");
                writer.println("- Need for parameter adjustment");
                writer.println();
                writer.println("Try adjusting detection parameters if signatures are expected.");
            } else {
                writer.println("Detected Signatures:");
                writer.println("-------------------");
                
                for (int i = 0; i < signatureRegions.size(); i++) {
                    Rect region = signatureRegions.get(i);
                    int area = region.width * region.height;
                    double aspectRatio = (double) region.width / region.height;
                    
                    writer.println("Signature " + (i + 1) + ":");
                    writer.println("  Position: (" + region.x + ", " + region.y + ")");
                    writer.println("  Dimensions: " + region.width + "x" + region.height + " pixels");
                    writer.println("  Area: " + area + " pixels");
                    writer.println("  Aspect Ratio: " + String.format("%.2f", aspectRatio));
                    writer.println("  Extracted to: 04_signature_" + (i + 1) + ".png");
                    writer.println();
                }
            }
            
            writer.println("Output Files:");
            writer.println("------------");
            writer.println("01_preprocessed.png - Preprocessed binary image");
            writer.println("02_lines_removed.png - Image with lines removed");
            writer.println("03_signature_detection.png - Visualization with detected regions");
            writer.println("04_signature_*.png - Individual extracted signatures");
            writer.println("detailed_report.txt - This report");
        }
        
        System.out.println("  Saved: " + reportPath);
    }
}
