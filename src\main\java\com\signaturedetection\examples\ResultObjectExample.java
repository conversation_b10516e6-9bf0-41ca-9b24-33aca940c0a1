package com.signaturedetection.examples;

import com.signaturedetection.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Example demonstrating how to use the SignatureDetectionResult object
 * to access coordinates and test against expected values.
 */
public class ResultObjectExample {
    private static final Logger logger = LoggerFactory.getLogger(ResultObjectExample.class);
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java ResultObjectExample <input-tiff-path>");
            System.out.println("Example: java ResultObjectExample document.tiff");
            return;
        }
        
        String inputPath = args[0];
        String outputDir = "./result_example_output/";
        
        try {
            demonstrateResultObjectUsage(inputPath, outputDir);
        } catch (Exception e) {
            logger.error("Error in result object example: {}", e.getMessage(), e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Demonstrates various ways to use the SignatureDetectionResult object.
     */
    public static void demonstrateResultObjectUsage(String inputPath, String outputDir) throws IOException {
        System.out.println("=== SignatureDetectionResult Object Usage Example ===");
        System.out.println("Input: " + inputPath);
        System.out.println("Output: " + outputDir);
        System.out.println();
        
        // Process the document and get structured results
        SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithResult(inputPath, outputDir);
        
        // Check if processing was successful
        if (!result.isSuccess()) {
            System.out.println("Processing failed: " + result.getErrorMessage());
            return;
        }
        
        // Display basic information
        System.out.println("Processing successful!");
        System.out.println("Processing time: " + result.getStats().getProcessingTimeMs() + "ms");
        System.out.println("Image dimensions: " + result.getStats().getImageWidth() + "x" + result.getStats().getImageHeight());
        System.out.println("Total contours found: " + result.getStats().getTotalContours());
        System.out.println("Signatures detected: " + result.getSignatureCount());
        System.out.println();
        
        // Example 1: Access individual signatures by coordinates
        System.out.println("=== Example 1: Individual Signature Access ===");
        for (SignatureDetectionResult.DetectedSignature signature : result.getSignatures()) {
            System.out.println("Signature " + signature.getId() + ":");
            System.out.println("  Position: " + signature.getTopLeft());
            System.out.println("  Size: " + signature.getWidth() + "x" + signature.getHeight());
            System.out.println("  Center: " + signature.getCenter());
            System.out.println("  Area: " + signature.getArea() + " pixels");
            System.out.println("  Aspect Ratio: " + String.format("%.2f", signature.getAspectRatio()));
            System.out.println("  Bounds: " + signature.getTopLeft() + " to " + signature.getBottomRight());
            System.out.println();
        }
        
        // Example 2: Test against expected coordinates
        System.out.println("=== Example 2: Coordinate Validation ===");
        testAgainstExpectedCoordinates(result);
        
        // Example 3: Filter signatures by properties
        System.out.println("=== Example 3: Filtering Signatures ===");
        filterSignaturesByProperties(result);
        
        // Example 4: Spatial analysis
        System.out.println("=== Example 4: Spatial Analysis ===");
        performSpatialAnalysis(result);
        
        // Example 5: Generate test assertions
        System.out.println("=== Example 5: Generated Test Assertions ===");
        generateTestAssertions(result);
        
        // Save detailed results
        String detailedResultsPath = outputDir + "/detailed_results.txt";
        saveDetailedResults(result, detailedResultsPath);
        System.out.println("Detailed results saved to: " + detailedResultsPath);
    }
    
    /**
     * Example of testing against expected coordinates.
     */
    private static void testAgainstExpectedCoordinates(SignatureDetectionResult result) {
        // Define expected signature locations (replace with your actual expected values)
        ExpectedSignature[] expectedSignatures = {
            new ExpectedSignature(100, 200, 120, 40),  // x, y, width, height
            new ExpectedSignature(300, 450, 100, 35),
            new ExpectedSignature(500, 600, 140, 50)
        };
        
        System.out.println("Testing against " + expectedSignatures.length + " expected signatures:");
        
        for (ExpectedSignature expected : expectedSignatures) {
            SignatureDetectionResult.DetectedSignature closest = findClosestSignature(result, expected.x, expected.y);
            
            if (closest != null) {
                double distance = Math.sqrt(Math.pow(closest.getX() - expected.x, 2) + 
                                          Math.pow(closest.getY() - expected.y, 2));
                
                System.out.println("Expected at (" + expected.x + "," + expected.y + ") -> " +
                                 "Found at " + closest.getTopLeft() + " (distance: " + String.format("%.1f", distance) + " pixels)");
                
                // Check if within acceptable tolerance
                int tolerance = 50; // pixels
                if (distance <= tolerance) {
                    System.out.println("  ✓ MATCH (within " + tolerance + " pixel tolerance)");
                } else {
                    System.out.println("  ✗ NO MATCH (exceeds " + tolerance + " pixel tolerance)");
                }
            } else {
                System.out.println("Expected at (" + expected.x + "," + expected.y + ") -> NOT FOUND");
            }
        }
        System.out.println();
    }
    
    /**
     * Example of filtering signatures by various properties.
     */
    private static void filterSignaturesByProperties(SignatureDetectionResult result) {
        // Filter by size
        var largeSignatures = result.getSignaturesLargerThan(5000);
        System.out.println("Large signatures (>5000 pixels): " + largeSignatures.size());
        
        // Filter by aspect ratio (wide signatures)
        var wideSignatures = result.getSignaturesByAspectRatio(3.0, 10.0);
        System.out.println("Wide signatures (aspect ratio 3-10): " + wideSignatures.size());
        
        // Filter by region (top half of document)
        if (result.getStats() != null) {
            int imageHeight = result.getStats().getImageHeight();
            int imageWidth = result.getStats().getImageWidth();
            var topHalfSignatures = result.getSignaturesInArea(0, 0, imageWidth, imageHeight / 2);
            System.out.println("Signatures in top half: " + topHalfSignatures.size());
            
            var bottomHalfSignatures = result.getSignaturesInArea(0, imageHeight / 2, imageWidth, imageHeight / 2);
            System.out.println("Signatures in bottom half: " + bottomHalfSignatures.size());
        }
        System.out.println();
    }
    
    /**
     * Example of spatial analysis between signatures.
     */
    private static void performSpatialAnalysis(SignatureDetectionResult result) {
        var signatures = result.getSignatures();
        
        if (signatures.size() >= 2) {
            System.out.println("Distances between signatures:");
            for (int i = 0; i < signatures.size(); i++) {
                for (int j = i + 1; j < signatures.size(); j++) {
                    double distance = signatures.get(i).distanceTo(signatures.get(j));
                    System.out.println("  Signature " + signatures.get(i).getId() + 
                                     " to Signature " + signatures.get(j).getId() + 
                                     ": " + String.format("%.1f", distance) + " pixels");
                }
            }
            
            // Check for overlapping signatures
            System.out.println("Overlap analysis:");
            boolean foundOverlap = false;
            for (int i = 0; i < signatures.size(); i++) {
                for (int j = i + 1; j < signatures.size(); j++) {
                    if (signatures.get(i).overlaps(signatures.get(j))) {
                        System.out.println("  ⚠ Signature " + signatures.get(i).getId() + 
                                         " overlaps with Signature " + signatures.get(j).getId());
                        foundOverlap = true;
                    }
                }
            }
            if (!foundOverlap) {
                System.out.println("  ✓ No overlapping signatures detected");
            }
        }
        System.out.println();
    }
    
    /**
     * Generate test assertions that can be used in unit tests.
     */
    private static void generateTestAssertions(SignatureDetectionResult result) {
        System.out.println("Generated test assertions (copy to your test code):");
        System.out.println("// Test signature count");
        System.out.println("assertEquals(" + result.getSignatureCount() + ", result.getSignatureCount());");
        System.out.println();
        
        for (SignatureDetectionResult.DetectedSignature sig : result.getSignatures()) {
            System.out.println("// Test signature " + sig.getId());
            System.out.println("DetectedSignature sig" + sig.getId() + " = result.getSignatureById(" + sig.getId() + ");");
            System.out.println("assertNotNull(sig" + sig.getId() + ");");
            System.out.println("assertEquals(" + sig.getX() + ", sig" + sig.getId() + ".getX(), 20); // ±20 pixel tolerance");
            System.out.println("assertEquals(" + sig.getY() + ", sig" + sig.getId() + ".getY(), 20);");
            System.out.println("assertTrue(sig" + sig.getId() + ".getArea() >= " + (int)(sig.getArea() * 0.8) + ");");
            System.out.println();
        }
    }
    
    /**
     * Save detailed results to a file.
     */
    private static void saveDetailedResults(SignatureDetectionResult result, String filePath) throws IOException {
        try (java.io.PrintWriter writer = new java.io.PrintWriter(filePath)) {
            writer.println("Detailed Signature Detection Results");
            writer.println("===================================");
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println();
            
            writer.println("JSON Results:");
            writer.println(result.toJsonString());
            writer.println();
            
            writer.println("Detailed Analysis:");
            writer.println("-----------------");
            
            if (result.isSuccess()) {
                writer.println("Processing Status: SUCCESS");
                writer.println("Processing Time: " + result.getStats().getProcessingTimeMs() + "ms");
                writer.println("Image Dimensions: " + result.getStats().getImageWidth() + "x" + result.getStats().getImageHeight());
                writer.println("Total Contours: " + result.getStats().getTotalContours());
                writer.println("Lines Removed: " + result.getStats().isLinesRemoved());
                writer.println("Input Path: " + result.getStats().getInputPath());
                writer.println();
                
                for (SignatureDetectionResult.DetectedSignature sig : result.getSignatures()) {
                    writer.println("Signature " + sig.getId() + " Details:");
                    writer.println("  Coordinates: " + sig.getTopLeft() + " to " + sig.getBottomRight());
                    writer.println("  Center: " + sig.getCenter());
                    writer.println("  Dimensions: " + sig.getWidth() + "x" + sig.getHeight());
                    writer.println("  Area: " + sig.getArea() + " pixels");
                    writer.println("  Aspect Ratio: " + String.format("%.3f", sig.getAspectRatio()));
                    writer.println();
                }
            } else {
                writer.println("Processing Status: FAILED");
                writer.println("Error Message: " + result.getErrorMessage());
            }
        }
    }
    
    // Helper classes and methods
    
    private static class ExpectedSignature {
        final int x, y, width, height;
        
        ExpectedSignature(int x, int y, int width, int height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
    }
    
    private static SignatureDetectionResult.DetectedSignature findClosestSignature(
            SignatureDetectionResult result, int expectedX, int expectedY) {
        
        SignatureDetectionResult.DetectedSignature closest = null;
        double minDistance = Double.MAX_VALUE;
        
        for (SignatureDetectionResult.DetectedSignature signature : result.getSignatures()) {
            double distance = Math.sqrt(Math.pow(signature.getX() - expectedX, 2) + 
                                      Math.pow(signature.getY() - expectedY, 2));
            if (distance < minDistance) {
                minDistance = distance;
                closest = signature;
            }
        }
        
        return closest;
    }
}
