# Signature Detection and Line Removal

A Java application for detecting and extracting signatures from TIFF images while removing unwanted lines from scanned documents.

## Features

- **TIFF Image Support**: Load and process TIFF images using both OpenCV and Java ImageIO
- **Advanced Noise Removal**: Comprehensive noise reduction including salt-and-pepper, speckle, and scanning artifacts
- **Line Removal**: Remove horizontal and vertical lines from scanned documents using morphological operations
- **Signature Detection**: Detect signature regions using contour analysis and connected component analysis
- **Signature Extraction**: Extract individual signatures as separate images
- **Customizable Parameters**: Fine-tune detection and noise removal parameters for different document types
- **Multiple Processing Modes**: Light, standard, aggressive, and advanced noise removal options
- **Comprehensive Output**: Generate processed images, extracted signatures, and detection reports

## Requirements

- Java 11 or higher
- Maven 3.6 or higher

## Dependencies

- OpenCV for Java (4.9.0)
- TwelveMonkeys ImageIO for enhanced TIFF support
- Apache Commons IO
- SLF4J with Logback for logging
- JUnit 5 for testing

## Installation

1. Clone or download the project
2. Build the project using Maven:

```bash
mvn clean compile
```

## Usage

### Basic Usage

Process a TIFF image with default parameters:

```bash
mvn exec:java -Dexec.args="path/to/your/document.tiff ./output/"
```

### Programmatic Usage

```java
import com.signaturedetection.*;

// Basic processing with result object (includes standard noise removal)
SignatureDetectionResult result = SignatureDetectionApp.processDocumentWithResult("document.tiff", "./output/");

// Advanced processing with comprehensive noise removal for degraded documents
SignatureDetectionResult advancedResult = SignatureDetectionApp.processDocumentWithAdvancedDenoising("noisy_document.tiff", "./output/");

// Custom noise removal configuration
NoiseRemover.NoiseRemovalConfig customConfig = new NoiseRemover.NoiseRemovalConfig();
customConfig.gaussianKernelSize = 5;
customConfig.medianKernelSize = 5;
customConfig.morphKernelSize = 3;
SignatureDetectionResult customResult = SignatureDetectionApp.processDocumentWithCustomDenoising("document.tiff", "./output/", customConfig);

// Check if processing was successful
if (result.isSuccess()) {
    System.out.println("Found " + result.getSignatureCount() + " signatures");

    // Access individual signatures with coordinates
    for (SignatureDetectionResult.DetectedSignature signature : result.getSignatures()) {
        System.out.println("Signature " + signature.getId() +
                          " at " + signature.getTopLeft() +
                          " size: " + signature.getWidth() + "x" + signature.getHeight());
    }

    // Get processing statistics
    System.out.println("Processing time: " + result.getStats().getProcessingTimeMs() + "ms");
    System.out.println("Image size: " + result.getStats().getImageWidth() + "x" + result.getStats().getImageHeight());
} else {
    System.out.println("Processing failed: " + result.getErrorMessage());
}

// Legacy processing (saves files but doesn't return structured results)
SignatureDetectionApp.processDocument("document.tiff", "./output/");

// Custom processing with fine-tuned parameters
SignatureDetectionApp.processDocumentCustom(
    "document.tiff",           // Input path
    "./output/",               // Output directory
    50,                        // Horizontal kernel size for line removal
    30,                        // Vertical kernel size for line removal
    1000,                      // Minimum signature area
    50000,                     // Maximum signature area
    1.5,                       // Minimum aspect ratio
    8.0                        // Maximum aspect ratio
);
```

### Individual Components

```java
import com.signaturedetection.*;
import org.opencv.core.Mat;
import org.opencv.core.Rect;
import java.util.List;

// Load and preprocess image with noise removal
Mat originalImage = ImageProcessor.loadTiffImage("document.tiff");
Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage); // Includes noise removal

// Advanced preprocessing for heavily degraded images
Mat advancedPreprocessed = ImageProcessor.preprocessImageAdvanced(originalImage);

// Custom noise removal
NoiseRemover.NoiseRemovalConfig config = NoiseRemover.NoiseRemovalConfig.createAggressiveConfig();
Mat customPreprocessed = ImageProcessor.preprocessImage(originalImage, config);

// Individual noise removal techniques
Mat denoisedImage = NoiseRemover.removeNoise(originalImage);
Mat saltPepperCleaned = NoiseRemover.removeSaltPepperNoise(originalImage, 5);
Mat speckleCleaned = NoiseRemover.removeSpeckleNoise(originalImage, 3);
Mat artifactsCleaned = NoiseRemover.removeScanningArtifacts(originalImage);

// Remove lines
Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);

// Detect signatures
List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);

// Extract signatures
List<Mat> signatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);

// Save results
ImageProcessor.saveImage(imageWithoutLines, "processed.png");
for (int i = 0; i < signatures.size(); i++) {
    ImageProcessor.saveImage(signatures.get(i), "signature_" + (i + 1) + ".png");
}
```

## Output Files

The application generates several output files:

- `processed_image.png`: The original image with lines removed
- `signature_detection_result.png`: Visualization showing detected signature regions highlighted in green
- `signature_1.png`, `signature_2.png`, etc.: Individual extracted signatures
- `detection_report.txt`: Text report with detection statistics and signature details
- `detection_results.json`: Structured JSON results with coordinates and statistics for testing

## Result Object for Testing

The `SignatureDetectionResult` object provides structured access to detection results:

```java
// Access detected signatures with coordinates
for (SignatureDetectionResult.DetectedSignature signature : result.getSignatures()) {
    // Get coordinates
    int x = signature.getX();
    int y = signature.getY();
    int width = signature.getWidth();
    int height = signature.getHeight();

    // Get calculated properties
    double area = signature.getArea();
    double aspectRatio = signature.getAspectRatio();

    // Get coordinate points
    Point topLeft = signature.getTopLeft();
    Point center = signature.getCenter();
    Point bottomRight = signature.getBottomRight();
}

// Filter signatures by properties
List<DetectedSignature> largeSignatures = result.getSignaturesLargerThan(5000);
List<DetectedSignature> wideSignatures = result.getSignaturesByAspectRatio(3.0, 8.0);
List<DetectedSignature> topHalfSignatures = result.getSignaturesInArea(0, 0, imageWidth, imageHeight/2);

// Test against expected coordinates
DetectedSignature signature = result.getSignatureById(1);
assertEquals(150, signature.getX(), 20); // ±20 pixel tolerance
assertEquals(200, signature.getY(), 20);

// Get processing statistics
ProcessingStats stats = result.getStats();
long processingTime = stats.getProcessingTimeMs();
int imageWidth = stats.getImageWidth();
int imageHeight = stats.getImageHeight();
```

## Algorithm Details

### Noise Removal

1. **Gaussian Denoising**: Reduces general noise while preserving edges
2. **Median Filtering**: Effectively removes salt-and-pepper noise
3. **Morphological Operations**: Removes small artifacts and speckle noise
4. **Connected Component Filtering**: Removes isolated noise components based on size
5. **Bilateral Filtering**: Edge-preserving smoothing for advanced denoising
6. **Non-Local Means**: Advanced texture-preserving denoising
7. **Scanning Artifact Removal**: Specialized removal of scanner-specific noise

### Line Removal

1. **Horizontal Line Detection**: Uses morphological operations with horizontal kernels to detect and remove horizontal lines
2. **Vertical Line Detection**: Uses morphological operations with vertical kernels to detect and remove vertical lines
3. **Preservation**: Carefully designed to preserve text and signature content while removing unwanted lines

### Signature Detection

1. **Preprocessing**: Applies Gaussian blur, adaptive thresholding, and morphological operations
2. **Contour Analysis**: Finds contours in the processed image
3. **Feature Filtering**: Filters contours based on:
   - Area (1000-50000 pixels by default)
   - Aspect ratio (1.5-8.0 by default)
   - Complexity (minimum 50 contour points)
   - Minimum dimensions (50x20 pixels)

### Customization Parameters

#### Noise Removal Parameters
- **Gaussian Kernel Size**: Controls the amount of Gaussian blur (3-7 typical range)
- **Median Kernel Size**: Size of median filter for salt-and-pepper noise (3-9 typical range)
- **Morphological Kernel Size**: Size of morphological operations for artifact removal (1-5 typical range)
- **Component Area Thresholds**: Min/max area for connected component filtering
- **Bilateral Filter Parameters**: Controls edge-preserving smoothing strength
- **Non-Local Means Parameters**: Advanced denoising strength and window sizes

#### Line Removal Parameters
- **Kernel Sizes**: Adjust horizontal and vertical kernel sizes for line removal based on line thickness
- **Area Constraints**: Set minimum and maximum signature areas based on expected signature sizes
- **Aspect Ratio**: Configure aspect ratio range based on signature characteristics
- **Morphological Operations**: Fine-tune preprocessing parameters for different image qualities

#### Noise Removal Configurations
- **Light Config**: Minimal noise removal for high-quality scans
- **Standard Config**: Balanced approach for typical office documents
- **Aggressive Config**: Strong noise removal for degraded documents
- **Custom Config**: User-defined parameters for specific noise types

## Testing

Run the test suite:

```bash
mvn test
```

The tests cover:
- Image preprocessing functionality
- Line removal algorithms
- Signature detection and extraction
- Custom parameter handling
- File I/O operations

## Troubleshooting

### Common Issues

1. **OpenCV Loading Issues**: Ensure the OpenCV native library is properly loaded
2. **TIFF Format Issues**: The application uses TwelveMonkeys ImageIO as a fallback for complex TIFF formats
3. **Memory Issues**: Large images may require increased JVM heap size: `-Xmx2g`
4. **No Signatures Detected**: Try adjusting detection parameters for your specific document type

### Performance Tips

- Use appropriate image resolution (300 DPI is typically sufficient)
- Consider preprocessing images to remove noise before processing
- Adjust kernel sizes based on the thickness of lines in your documents
- Fine-tune signature detection parameters based on your signature characteristics

## License

This project is provided as-is for educational and development purposes.
